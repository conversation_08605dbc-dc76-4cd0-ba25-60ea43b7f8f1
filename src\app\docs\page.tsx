import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { 
  Code, 
  Book, 
  Zap, 
  Shield, 
  Clock,
  ExternalLink,
  Copy
} from "lucide-react";
import Link from "next/link";

export default function DocsPage() {
  const apiEndpoints = [
    {
      method: "POST",
      endpoint: "/api/upload",
      description: "Upload hình ảnh từ file hoặc URL",
      parameters: [
        { name: "file", type: "File", required: true, description: "File hình ảnh (nếu upload từ file)" },
        { name: "url", type: "string", required: true, description: "URL hình ảnh (nếu upload từ URL)" },
      ],
      response: {
        success: true,
        data: {
          imageId: "uuid",
          url: "storage_path",
          publicUrl: "public_url"
        }
      }
    },
    {
      method: "POST",
      endpoint: "/api/process/remove-background",
      description: "Xóa background khỏi hình ảnh",
      parameters: [
        { name: "imageId", type: "string", required: true, description: "ID của hình ảnh đã upload" },
      ],
      response: {
        success: true,
        data: {
          imageId: "uuid",
          backgroundRemovedUrl: "processed_url",
          status: "background_removed"
        }
      }
    },
    {
      method: "POST",
      endpoint: "/api/process/upscale",
      description: "Tăng độ phân giải hình ảnh",
      parameters: [
        { name: "imageId", type: "string", required: true, description: "ID của hình ảnh" },
        { name: "scale", type: "number", required: false, description: "Hệ số phóng to (mặc định: 2)" },
      ],
      response: {
        success: true,
        data: {
          imageId: "uuid",
          upscaledUrl: "upscaled_url",
          status: "upscaled"
        }
      }
    },
    {
      method: "POST",
      endpoint: "/api/process/generate-background",
      description: "Tạo background mới cho sản phẩm",
      parameters: [
        { name: "imageId", type: "string", required: true, description: "ID của hình ảnh" },
        { name: "prompt", type: "string", required: false, description: "Mô tả background mong muốn" },
      ],
      response: {
        success: true,
        data: {
          imageId: "uuid",
          finalUrl: "final_url",
          status: "completed"
        }
      }
    },
    {
      method: "GET",
      endpoint: "/api/process/result/{id}",
      description: "Lấy kết quả xử lý hình ảnh",
      parameters: [
        { name: "id", type: "string", required: true, description: "ID của hình ảnh" },
      ],
      response: {
        success: true,
        data: {
          id: "uuid",
          originalUrl: "original_url",
          backgroundRemovedUrl: "processed_url",
          upscaledUrl: "upscaled_url",
          finalUrl: "final_url",
          status: "completed",
          createdAt: "timestamp"
        }
      }
    }
  ];

  const codeExamples = {
    javascript: `// Upload và xử lý hình ảnh
async function processImage(file) {
  try {
    // 1. Upload hình ảnh
    const formData = new FormData();
    formData.append('file', file);
    
    const uploadResponse = await fetch('/api/upload', {
      method: 'POST',
      body: formData
    });
    const uploadResult = await uploadResponse.json();
    
    if (!uploadResult.success) {
      throw new Error(uploadResult.error);
    }
    
    const imageId = uploadResult.data.imageId;
    
    // 2. Xóa background
    const removeResponse = await fetch('/api/process/remove-background', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ imageId })
    });
    
    // 3. Tăng độ phân giải
    const upscaleResponse = await fetch('/api/process/upscale', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ imageId, scale: 2 })
    });
    
    // 4. Tạo background mới
    const generateResponse = await fetch('/api/process/generate-background', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({ 
        imageId,
        prompt: 'professional product photography background'
      })
    });
    
    // 5. Lấy kết quả
    const resultResponse = await fetch(\`/api/process/result/\${imageId}\`);
    const result = await resultResponse.json();
    
    return result.data;
    
  } catch (error) {
    console.error('Processing error:', error);
    throw error;
  }
}`,
    python: `import requests
import json

def process_image(file_path):
    """Upload và xử lý hình ảnh"""
    base_url = "https://your-domain.com"
    
    try:
        # 1. Upload hình ảnh
        with open(file_path, 'rb') as f:
            files = {'file': f}
            upload_response = requests.post(f"{base_url}/api/upload", files=files)
            upload_result = upload_response.json()
            
        if not upload_result['success']:
            raise Exception(upload_result['error'])
            
        image_id = upload_result['data']['imageId']
        
        # 2. Xóa background
        remove_response = requests.post(
            f"{base_url}/api/process/remove-background",
            json={'imageId': image_id}
        )
        
        # 3. Tăng độ phân giải
        upscale_response = requests.post(
            f"{base_url}/api/process/upscale",
            json={'imageId': image_id, 'scale': 2}
        )
        
        # 4. Tạo background mới
        generate_response = requests.post(
            f"{base_url}/api/process/generate-background",
            json={
                'imageId': image_id,
                'prompt': 'professional product photography background'
            }
        )
        
        # 5. Lấy kết quả
        result_response = requests.get(f"{base_url}/api/process/result/{image_id}")
        result = result_response.json()
        
        return result['data']
        
    except Exception as error:
        print(f"Processing error: {error}")
        raise`,
    curl: `# 1. Upload hình ảnh
curl -X POST "https://your-domain.com/api/upload" \\
  -F "file=@/path/to/your/image.jpg"

# Response: {"success": true, "data": {"imageId": "uuid", ...}}

# 2. Xóa background
curl -X POST "https://your-domain.com/api/process/remove-background" \\
  -H "Content-Type: application/json" \\
  -d '{"imageId": "your-image-id"}'

# 3. Tăng độ phân giải
curl -X POST "https://your-domain.com/api/process/upscale" \\
  -H "Content-Type: application/json" \\
  -d '{"imageId": "your-image-id", "scale": 2}'

# 4. Tạo background mới
curl -X POST "https://your-domain.com/api/process/generate-background" \\
  -H "Content-Type: application/json" \\
  -d '{"imageId": "your-image-id", "prompt": "professional background"}'

# 5. Lấy kết quả
curl "https://your-domain.com/api/process/result/your-image-id"`
  };

  return (
    <div className="container py-8">
      <div className="mx-auto max-w-6xl space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <Badge variant="secondary" className="mb-4">
            <Book className="mr-1 h-3 w-3" />
            API Documentation
          </Badge>
          <h1 className="text-4xl font-bold tracking-tight">
            Tài Liệu{" "}
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              API
            </span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Tích hợp Background Generator vào ứng dụng của bạn với API đơn giản và mạnh mẽ
          </p>
        </div>

        {/* Quick Start */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Zap className="h-5 w-5 text-yellow-600" />
              Bắt Đầu Nhanh
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-3">
              <div className="text-center space-y-2">
                <div className="mx-auto w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 font-bold">
                  1
                </div>
                <h3 className="font-medium">Upload Hình Ảnh</h3>
                <p className="text-sm text-muted-foreground">
                  Gửi file hoặc URL hình ảnh đến API
                </p>
              </div>
              <div className="text-center space-y-2">
                <div className="mx-auto w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 font-bold">
                  2
                </div>
                <h3 className="font-medium">Xử Lý</h3>
                <p className="text-sm text-muted-foreground">
                  Gọi các API xử lý theo thứ tự
                </p>
              </div>
              <div className="text-center space-y-2">
                <div className="mx-auto w-8 h-8 bg-green-100 rounded-full flex items-center justify-center text-green-600 font-bold">
                  3
                </div>
                <h3 className="font-medium">Nhận Kết Quả</h3>
                <p className="text-sm text-muted-foreground">
                  Lấy hình ảnh đã xử lý
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* API Endpoints */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Code className="h-5 w-5 text-blue-600" />
              API Endpoints
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {apiEndpoints.map((endpoint, index) => (
                <div key={index} className="border rounded-lg p-4 space-y-3">
                  <div className="flex items-center gap-2">
                    <Badge variant={endpoint.method === "GET" ? "default" : "secondary"}>
                      {endpoint.method}
                    </Badge>
                    <code className="text-sm bg-muted px-2 py-1 rounded">
                      {endpoint.endpoint}
                    </code>
                  </div>
                  
                  <p className="text-sm text-muted-foreground">
                    {endpoint.description}
                  </p>
                  
                  {endpoint.parameters.length > 0 && (
                    <div>
                      <h4 className="text-sm font-medium mb-2">Parameters:</h4>
                      <div className="space-y-1">
                        {endpoint.parameters.map((param, paramIndex) => (
                          <div key={paramIndex} className="text-xs">
                            <code className="bg-muted px-1 rounded">{param.name}</code>
                            <span className="text-muted-foreground"> ({param.type})</span>
                            {param.required && <span className="text-red-500"> *</span>}
                            <span className="text-muted-foreground"> - {param.description}</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                  
                  <div>
                    <h4 className="text-sm font-medium mb-2">Response:</h4>
                    <pre className="text-xs bg-muted p-2 rounded overflow-x-auto">
                      {JSON.stringify(endpoint.response, null, 2)}
                    </pre>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Code Examples */}
        <Card>
          <CardHeader>
            <CardTitle>Ví Dụ Code</CardTitle>
          </CardHeader>
          <CardContent>
            <Tabs defaultValue="javascript">
              <TabsList className="grid w-full grid-cols-3">
                <TabsTrigger value="javascript">JavaScript</TabsTrigger>
                <TabsTrigger value="python">Python</TabsTrigger>
                <TabsTrigger value="curl">cURL</TabsTrigger>
              </TabsList>
              
              {Object.entries(codeExamples).map(([lang, code]) => (
                <TabsContent key={lang} value={lang}>
                  <div className="relative">
                    <pre className="text-sm bg-muted p-4 rounded-lg overflow-x-auto">
                      <code>{code}</code>
                    </pre>
                    <Button
                      variant="outline"
                      size="sm"
                      className="absolute top-2 right-2"
                      onClick={() => navigator.clipboard.writeText(code)}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                </TabsContent>
              ))}
            </Tabs>
          </CardContent>
        </Card>

        {/* Rate Limits & Guidelines */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Clock className="h-5 w-5 text-orange-600" />
                Rate Limits
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex justify-between">
                <span className="text-sm">Requests per minute:</span>
                <Badge variant="outline">60</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Concurrent processing:</span>
                <Badge variant="outline">5</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Max file size:</span>
                <Badge variant="outline">10MB</Badge>
              </div>
              <div className="flex justify-between">
                <span className="text-sm">Supported formats:</span>
                <Badge variant="outline">JPG, PNG, WEBP</Badge>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Shield className="h-5 w-5 text-green-600" />
                Bảo Mật
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <p className="text-sm text-muted-foreground">
                • Tất cả requests phải sử dụng HTTPS
              </p>
              <p className="text-sm text-muted-foreground">
                • Hình ảnh được tự động xóa sau 24 giờ
              </p>
              <p className="text-sm text-muted-foreground">
                • Không lưu trữ thông tin cá nhân
              </p>
              <p className="text-sm text-muted-foreground">
                • API key sẽ được cung cấp trong tương lai
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Support */}
        <Card>
          <CardHeader>
            <CardTitle>Cần Hỗ Trợ?</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col sm:flex-row gap-4 items-center justify-between">
            <p className="text-muted-foreground">
              Có câu hỏi về API hoặc cần hỗ trợ tích hợp? Chúng tôi sẵn sàng giúp đỡ.
            </p>
            <div className="flex gap-2">
              <Button variant="outline" asChild>
                <Link href="/contact">
                  <ExternalLink className="mr-2 h-4 w-4" />
                  Liên Hệ
                </Link>
              </Button>
              <Button asChild>
                <Link href="/upload">
                  Thử Ngay
                </Link>
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
