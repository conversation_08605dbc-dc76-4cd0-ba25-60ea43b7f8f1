# Workflow Changes - Background Generator

## Tổng Quan Thay Đổi
Chuyển từ workflow tự động sang workflow thủ công với các nút riêng biệt để kiểm soát từng bước.

## Trước Khi Thay Đổi
- Workflow tự động chạy 5 bước liên tiếp
- Không thể kiểm soát hoặc test từng bước riêng lẻ
- Khó debug khi có lỗi xảy ra

## Sau Khi Thay Đổi
- 5 nút riêng biệt cho từng bước
- Hiển thị link tạm thời sau mỗi bước thành công
- Có thể test và debug từng bước
- Kiểm soát hoàn toàn quá trình xử lý

## Các Bước Workflow Mới

### 1. Upload (Tải lên)
- **Nút**: "Tải lên hình ảnh"
- **API**: `/api/upload`
- **<PERSON><PERSON><PERSON> quả**: <PERSON> hình ảnh gốc
- **Tr<PERSON>ng thái**: `uploaded`

### 2. Remove Background (Xóa background)
- **Nút**: "Xóa background"
- **API**: `/api/process/remove-background`
- **Kết quả**: Link hình ảnh đã xóa background
- **Trạng thái**: `background_removed`

### 3. Upscale (Tăng độ phân giải)
- **Nút**: "Tăng độ phân giải"
- **API**: `/api/process/upscale`
- **Kết quả**: Link hình ảnh độ phân giải cao
- **Trạng thái**: `upscaled`

### 4. Generate Background (Tạo background mới)
- **Nút**: "Tạo background mới"
- **API**: `/api/process/generate-background`
- **Kết quả**: Link hình ảnh với background mới
- **Trạng thái**: `background_generated`

### 5. Finalize (Hoàn thiện)
- **Nút**: "Hoàn thiện"
- **Kết quả**: Hiển thị tất cả kết quả và tùy chọn tải xuống
- **Trạng thái**: `completed`

## Files Đã Thay Đổi

### 1. src/app/upload/page.tsx ✅ HOÀN THÀNH
- **Thay đổi**: Tách logic xử lý tự động thành các nút riêng biệt
- **Xóa**: Logic workflow tự động cũ
- **Thêm**: Sử dụng StepByStepProcessor component
- **Đơn giản hóa**: State management chỉ còn idle/selecting/processing/completed

### 2. src/components/upload/step-by-step-processor.tsx ✅ HOÀN THÀNH
- **Mục đích**: Component chuyên xử lý từng bước
- **Tính năng**:
  - 5 nút riêng biệt cho từng bước
  - Hiển thị trạng thái từng bước (idle/processing/completed/error)
  - Hiển thị link tạm thời sau mỗi bước thành công
  - Error handling riêng cho từng bước
  - Toast notifications cho feedback
  - Disable logic để đảm bảo thứ tự thực hiện

### 3. WORKFLOW_CHANGES.md ✅ HOÀN THÀNH
- **Mục đích**: Ghi lại tất cả thay đổi và hướng dẫn sử dụng
- **Nội dung**: Chi tiết về workflow mới, API endpoints, và cách sử dụng

## Lợi Ích Của Thay Đổi

### 1. Kiểm Soát Tốt Hơn
- Test từng bước riêng lẻ
- Dễ dàng debug khi có lỗi
- Có thể dừng và tiếp tục bất kỳ lúc nào

### 2. User Experience Tốt Hơn
- Hiển thị kết quả tạm thời sau mỗi bước
- Người dùng có thể xem và đánh giá từng giai đoạn
- Không bị mất kết quả khi có lỗi ở bước sau

### 3. Development & Testing
- Dễ dàng test API riêng lẻ
- Debug nhanh chóng khi có vấn đề
- Có thể cải thiện từng bước độc lập

## Cách Sử Dụng Mới

1. **Upload**: Chọn file hoặc nhập URL → Nhấn "Tải lên hình ảnh"
2. **Remove Background**: Nhấn "Xóa background" → Xem link kết quả
3. **Upscale**: Nhấn "Tăng độ phân giải" → Xem link kết quả
4. **Generate Background**: Nhấn "Tạo background mới" → Xem link kết quả
5. **Finalize**: Nhấn "Hoàn thiện" → Xem tất cả kết quả và tải xuống

## Notes cho Developer

### State Management
```typescript
interface StepState {
  status: 'idle' | 'processing' | 'completed' | 'error';
  result?: string; // URL của kết quả
  error?: string;
}

interface WorkflowState {
  upload: StepState;
  removeBackground: StepState;
  upscale: StepState;
  generateBackground: StepState;
  finalize: StepState;
  currentImageId?: string;
}
```

### Error Handling
- Mỗi bước có error handling riêng
- Hiển thị lỗi cụ thể cho từng bước
- Có thể retry từng bước riêng lẻ

### Future Improvements
- Thêm tùy chọn skip một số bước
- Cho phép chỉnh sửa parameters cho từng bước
- Lưu workflow state để tiếp tục sau

## Hướng Dẫn Test

### 1. Test Cơ Bản
1. **Khởi động server**: `npm run dev`
2. **Truy cập**: http://localhost:3000/upload
3. **Upload file**: Chọn file hoặc nhập URL
4. **Test từng bước**:
   - Nhấn "Tải lên hình ảnh" → Kiểm tra link kết quả
   - Nhấn "Xóa background" → Kiểm tra link kết quả
   - Nhấn "Tăng độ phân giải" → Kiểm tra link kết quả
   - Nhấn "Tạo background mới" → Kiểm tra link kết quả
   - Nhấn "Hoàn thiện" → Xem kết quả cuối cùng

### 2. Test Error Handling
- Test với file không hợp lệ
- Test với URL không tồn tại
- Test khi API bị lỗi (tắt internet)
- Test retry sau khi có lỗi

### 3. Test UI/UX
- Kiểm tra disable logic của các nút
- Kiểm tra hiển thị trạng thái
- Kiểm tra toast notifications
- Kiểm tra responsive design

### 4. Kiểm Tra API Endpoints
```bash
# Test upload
curl -X POST http://localhost:3000/api/upload \
  -F "file=@test-image.jpg"

# Test remove background
curl -X POST http://localhost:3000/api/process/remove-background \
  -H "Content-Type: application/json" \
  -d '{"imageId": "your-image-id"}'

# Test upscale
curl -X POST http://localhost:3000/api/process/upscale \
  -H "Content-Type: application/json" \
  -d '{"imageId": "your-image-id"}'

# Test generate background
curl -X POST http://localhost:3000/api/process/generate-background \
  -H "Content-Type: application/json" \
  -d '{"imageId": "your-image-id"}'

# Test get result
curl http://localhost:3000/api/process/result/your-image-id
```

## Troubleshooting

### Lỗi Thường Gặp
1. **"Image ID is required"**: Bước upload chưa hoàn thành
2. **"Background must be removed first"**: Thứ tự bước bị sai
3. **"Failed to get final result"**: API endpoint không đúng
4. **Network errors**: Kiểm tra kết nối internet và Runware API

### Debug Tips
- Mở Developer Tools để xem network requests
- Kiểm tra console logs cho errors
- Verify API responses trong Network tab
- Test từng API endpoint riêng lẻ với curl

## Kết Luận
Workflow mới đã được triển khai thành công với:
- ✅ 5 nút riêng biệt cho từng bước
- ✅ Hiển thị link tạm thời sau mỗi bước
- ✅ Error handling và retry capability
- ✅ Kiểm soát hoàn toàn quá trình xử lý
- ✅ Documentation đầy đủ cho maintenance

Bây giờ bạn có thể test và kiểm soát từng bước một cách độc lập!
