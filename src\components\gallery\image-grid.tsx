"use client";

import { useState } from "react";
import Image from "next/image";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  Download, 
  Eye, 
  Trash2, 
  Calendar,
  ExternalLink
} from "lucide-react";
import { ProcessedImage } from "@/types";
import { useToast } from "@/hooks/use-toast";

interface ImageGridProps {
  images: ProcessedImage[];
  onImageClick?: (image: ProcessedImage) => void;
  onDownload?: (url: string, filename: string) => void;
  onDelete?: (imageId: string) => void;
}

export function ImageGrid({ 
  images, 
  onImageClick, 
  onDownload,
  onDelete 
}: ImageGridProps) {
  const [hoveredImage, setHoveredImage] = useState<string | null>(null);
  const { toast } = useToast();

  const handleDownload = async (url: string, filename: string, e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (onDownload) {
      onDownload(url, filename);
    } else {
      try {
        const response = await fetch(url);
        const blob = await response.blob();
        const downloadUrl = URL.createObjectURL(blob);
        
        const link = document.createElement("a");
        link.href = downloadUrl;
        link.download = filename;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        
        URL.revokeObjectURL(downloadUrl);
        
        toast({
          title: "Tải xuống thành công",
          description: `Đã tải xuống ${filename}`,
        });
      } catch (error) {
        toast({
          title: "Lỗi tải xuống",
          description: "Không thể tải xuống file",
          variant: "destructive",
        });
      }
    }
  };

  const handleDelete = (imageId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    
    if (onDelete) {
      onDelete(imageId);
    }
  };

  const handleViewFullSize = (url: string, e: React.MouseEvent) => {
    e.stopPropagation();
    window.open(url, "_blank");
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge variant="default" className="bg-green-100 text-green-800">Hoàn thành</Badge>;
      case "processing":
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Đang xử lý</Badge>;
      case "error":
        return <Badge variant="destructive">Lỗi</Badge>;
      default:
        return <Badge variant="secondary">{status}</Badge>;
    }
  };

  if (images.length === 0) {
    return (
      <div className="text-center py-12">
        <div className="mx-auto h-24 w-24 rounded-full bg-gray-100 flex items-center justify-center mb-4">
          <Eye className="h-12 w-12 text-gray-400" />
        </div>
        <h3 className="text-lg font-medium text-gray-900 mb-2">
          Chưa có hình ảnh nào
        </h3>
        <p className="text-gray-500 mb-4">
          Bắt đầu bằng cách upload hình ảnh đầu tiên của bạn
        </p>
        <Button asChild>
          <a href="/upload">
            Upload Hình Ảnh
          </a>
        </Button>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {images.map((image) => (
        <Card
          key={image.id}
          className="group cursor-pointer transition-all hover:shadow-lg"
          onClick={() => onImageClick?.(image)}
          onMouseEnter={() => setHoveredImage(image.id)}
          onMouseLeave={() => setHoveredImage(null)}
        >
          <CardContent className="p-0">
            <div className="relative aspect-square overflow-hidden rounded-t-lg">
              <Image
                src={image.finalUrl || image.backgroundRemovedUrl || image.originalUrl}
                alt="Processed image"
                fill
                className="object-cover transition-transform group-hover:scale-105"
              />
              
              {/* Overlay with actions */}
              {hoveredImage === image.id && (
                <div className="absolute inset-0 bg-black/40 flex items-center justify-center gap-2 transition-opacity">
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={(e) => handleViewFullSize(
                      image.finalUrl || image.backgroundRemovedUrl || image.originalUrl, 
                      e
                    )}
                  >
                    <ExternalLink className="h-4 w-4" />
                  </Button>
                  
                  {image.finalUrl && (
                    <Button
                      size="sm"
                      variant="secondary"
                      onClick={(e) => handleDownload(
                        image.finalUrl!, 
                        `background-generated-${image.id}.png`, 
                        e
                      )}
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                  )}
                  
                  {onDelete && (
                    <Button
                      size="sm"
                      variant="destructive"
                      onClick={(e) => handleDelete(image.id, e)}
                    >
                      <Trash2 className="h-4 w-4" />
                    </Button>
                  )}
                </div>
              )}
              
              {/* Status badge */}
              <div className="absolute top-2 left-2">
                {getStatusBadge(image.status)}
              </div>
            </div>
            
            {/* Image info */}
            <div className="p-4">
              <div className="flex items-center justify-between text-sm text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {new Date(image.createdAt).toLocaleDateString('vi-VN')}
                </div>
                <span className="text-xs">
                  ID: {image.id.slice(0, 8)}...
                </span>
              </div>
              
              {/* Processing steps indicator */}
              <div className="mt-2 flex gap-1">
                <div className={`h-1 w-full rounded ${image.originalUrl ? 'bg-green-500' : 'bg-gray-200'}`} />
                <div className={`h-1 w-full rounded ${image.backgroundRemovedUrl ? 'bg-green-500' : 'bg-gray-200'}`} />
                <div className={`h-1 w-full rounded ${image.upscaledUrl ? 'bg-green-500' : 'bg-gray-200'}`} />
                <div className={`h-1 w-full rounded ${image.finalUrl ? 'bg-green-500' : 'bg-gray-200'}`} />
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
