import { NextRequest, NextResponse } from "next/server";
import { getProcessedImage, updateProcessedImage } from "@/lib/supabase";
import { generateBackground } from "@/lib/runware";
import { ApiResponse } from "@/types";

export async function POST(request: NextRequest) {
  try {
    const { 
      imageId, 
      prompt = "professional product photography background, clean, modern, high quality" 
    } = await request.json();

    if (!imageId) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: "Image ID is required",
      }, { status: 400 });
    }

    // Get image record from database
    const imageRecord = await getProcessedImage(imageId);
    
    if (!imageRecord) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: "Image not found",
      }, { status: 404 });
    }

    // Use upscaled image if available, otherwise use background removed image
    const sourceUrl = imageRecord.upscaled_url || imageRecord.background_removed_url;
    
    if (!sourceUrl) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: "No processed image available for background generation",
      }, { status: 400 });
    }

    // Update status to processing
    await updateProcessedImage(imageId, {
      status: "processing_background_generation",
    });

    try {
      // Generate background using Runware API
      const finalUrl = await generateBackground(sourceUrl, prompt);

      // Update database with result
      const updatedRecord = await updateProcessedImage(imageId, {
        final_url: finalUrl,
        status: "completed",
      });

      return NextResponse.json<ApiResponse>({
        success: true,
        data: {
          imageId: updatedRecord.id,
          finalUrl: finalUrl,
          status: updatedRecord.status,
        },
        message: "Background generated successfully",
      });

    } catch (runwareError) {
      // Update status to error
      await updateProcessedImage(imageId, {
        status: "error",
      });

      throw runwareError;
    }

  } catch (error) {
    console.error("Generate background error:", error);
    
    return NextResponse.json<ApiResponse>({
      success: false,
      error: error instanceof Error ? error.message : "Background generation failed",
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json<ApiResponse>({
    success: false,
    error: "Method not allowed",
  }, { status: 405 });
}
