"use client";

import { Progress } from "@/components/ui/progress";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  CheckCircle, 
  Circle, 
  Loader2, 
  XCircle, 
  Upload,
  Scissors,
  Zap,
  Sparkles,
  Download,
  X
} from "lucide-react";
import { ProcessingStep } from "@/types";

interface ProcessingProgressProps {
  steps: ProcessingStep[];
  onCancel?: () => void;
  canCancel?: boolean;
}

const stepIcons = {
  upload: Upload,
  removeBackground: Scissors,
  upscale: Zap,
  generateBackground: Sparkles,
  finalize: Download,
};

const stepNames = {
  upload: "Tải lên",
  removeBackground: "Xóa background",
  upscale: "Tăng độ phân giải",
  generateBackground: "Tạo background mới",
  finalize: "Hoàn thiện",
};

export function ProcessingProgress({ 
  steps, 
  onCancel, 
  canCancel = true 
}: ProcessingProgressProps) {
  const totalSteps = steps.length;
  const completedSteps = steps.filter(step => step.status === 'completed').length;
  const currentStep = steps.find(step => step.status === 'processing');
  const errorStep = steps.find(step => step.status === 'error');
  
  const overallProgress = totalSteps > 0 ? (completedSteps / totalSteps) * 100 : 0;

  const getStepIcon = (step: ProcessingStep) => {
    const IconComponent = stepIcons[step.id as keyof typeof stepIcons] || Circle;
    
    switch (step.status) {
      case 'completed':
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case 'processing':
        return <Loader2 className="h-5 w-5 text-blue-600 animate-spin" />;
      case 'error':
        return <XCircle className="h-5 w-5 text-red-600" />;
      default:
        return <IconComponent className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStepBadge = (status: ProcessingStep['status']) => {
    switch (status) {
      case 'completed':
        return <Badge variant="default" className="bg-green-100 text-green-800">Hoàn thành</Badge>;
      case 'processing':
        return <Badge variant="default" className="bg-blue-100 text-blue-800">Đang xử lý</Badge>;
      case 'error':
        return <Badge variant="destructive">Lỗi</Badge>;
      default:
        return <Badge variant="secondary">Chờ xử lý</Badge>;
    }
  };

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            {currentStep ? (
              <>
                <Loader2 className="h-5 w-5 animate-spin" />
                Đang xử lý hình ảnh...
              </>
            ) : errorStep ? (
              <>
                <XCircle className="h-5 w-5 text-red-600" />
                Xử lý thất bại
              </>
            ) : completedSteps === totalSteps ? (
              <>
                <CheckCircle className="h-5 w-5 text-green-600" />
                Hoàn thành
              </>
            ) : (
              "Chuẩn bị xử lý..."
            )}
          </CardTitle>
          
          {canCancel && onCancel && (currentStep || completedSteps < totalSteps) && (
            <Button
              variant="outline"
              size="sm"
              onClick={onCancel}
              className="text-red-600 hover:text-red-700"
            >
              <X className="h-4 w-4 mr-1" />
              Hủy
            </Button>
          )}
        </div>
        
        <div className="space-y-2">
          <div className="flex justify-between text-sm text-muted-foreground">
            <span>Tiến độ tổng thể</span>
            <span>{completedSteps}/{totalSteps} bước</span>
          </div>
          <Progress value={overallProgress} className="h-2" />
        </div>
      </CardHeader>
      
      <CardContent>
        <div className="space-y-4">
          {steps.map((step, index) => (
            <div key={step.id} className="flex items-center gap-4">
              <div className="flex-shrink-0">
                {getStepIcon(step)}
              </div>
              
              <div className="flex-1 min-w-0">
                <div className="flex items-center justify-between">
                  <h4 className="text-sm font-medium">
                    {stepNames[step.id as keyof typeof stepNames] || step.name}
                  </h4>
                  {getStepBadge(step.status)}
                </div>
                
                {step.message && (
                  <p className="text-xs text-muted-foreground mt-1">
                    {step.message}
                  </p>
                )}
                
                {step.status === 'processing' && step.progress > 0 && (
                  <div className="mt-2">
                    <Progress value={step.progress} className="h-1" />
                    <p className="text-xs text-muted-foreground mt-1">
                      {step.progress}%
                    </p>
                  </div>
                )}
              </div>
            </div>
          ))}
        </div>
        
        {errorStep && (
          <div className="mt-4 p-3 bg-red-50 border border-red-200 rounded-md">
            <p className="text-sm text-red-800">
              <strong>Lỗi:</strong> {errorStep.message || "Đã xảy ra lỗi trong quá trình xử lý"}
            </p>
          </div>
        )}
        
        {currentStep && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-md">
            <p className="text-sm text-blue-800">
              {currentStep.message || `Đang ${stepNames[currentStep.id as keyof typeof stepNames]?.toLowerCase()}...`}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
