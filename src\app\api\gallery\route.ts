import { NextRequest, NextResponse } from "next/server";
import { getAllProcessedImages } from "@/lib/supabase";
import { ApiResponse, ProcessedImage } from "@/types";

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get("limit") || "50");
    const offset = parseInt(searchParams.get("offset") || "0");
    const status = searchParams.get("status");

    // Get images from database
    let images = await getAllProcessedImages(limit, offset);

    // Filter by status if provided
    if (status && status !== "all") {
      images = images.filter(img => img.status === status);
    }

    // Transform database records to ProcessedImage type
    const processedImages: ProcessedImage[] = images.map(img => ({
      id: img.id,
      originalUrl: img.original_url,
      processedUrl: img.processed_url || "",
      backgroundRemovedUrl: img.background_removed_url || undefined,
      upscaledUrl: img.upscaled_url || undefined,
      finalUrl: img.final_url || undefined,
      createdAt: img.created_at || new Date().toISOString(),
      status: img.status as "processing" | "completed" | "error" || "processing",
      steps: [], // Steps are not stored in database
    }));

    return NextResponse.json<ApiResponse<{ images: ProcessedImage[], total: number }>>({
      success: true,
      data: {
        images: processedImages,
        total: processedImages.length,
      },
      message: "Images retrieved successfully",
    });

  } catch (error) {
    console.error("Gallery API error:", error);
    
    return NextResponse.json<ApiResponse>({
      success: false,
      error: error instanceof Error ? error.message : "Failed to get images",
    }, { status: 500 });
  }
}

export async function POST() {
  return NextResponse.json<ApiResponse>({
    success: false,
    error: "Method not allowed",
  }, { status: 405 });
}
