import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Sparkles, 
  Zap, 
  Shield, 
  Users, 
  Target,
  Heart,
  ArrowRight
} from "lucide-react";
import Link from "next/link";

export default function AboutPage() {
  return (
    <div className="container py-8">
      <div className="mx-auto max-w-4xl space-y-12">
        {/* Hero Section */}
        <div className="text-center space-y-4">
          <Badge variant="secondary" className="mb-4">
            <Sparkles className="mr-1 h-3 w-3" />
            Về Chúng Tôi
          </Badge>
          <h1 className="text-4xl font-bold tracking-tight">
            Tạo Background Chuyên <PERSON>hi<PERSON>{" "}
            <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
              Với AI
            </span>
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Chúng tôi sử dụng công nghệ AI tiên tiến để giúp bạn tạo ra những hình ảnh sản phẩm 
            chuyên nghiệp một cách nhanh chóng và dễ dàng.
          </p>
        </div>

        {/* Mission */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Target className="h-6 w-6 text-blue-600" />
              Sứ Mệnh Của Chúng Tôi
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-lg">
              Chúng tôi tin rằng mọi doanh nghiệp, từ startup nhỏ đến tập đoàn lớn, 
              đều xứng đáng có những hình ảnh sản phẩm chất lượng cao để thu hút khách hàng.
            </p>
            <p>
              Background Generator được tạo ra với mục tiêu dân chủ hóa việc tạo nội dung visual, 
              giúp bạn tiết kiệm thời gian và chi phí mà vẫn đạt được kết quả chuyên nghiệp.
            </p>
          </CardContent>
        </Card>

        {/* Technology */}
        <div className="space-y-6">
          <h2 className="text-2xl font-bold text-center">Công Nghệ Chúng Tôi Sử Dụng</h2>
          
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-purple-600" />
                  Runware AI
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Sử dụng API Runware để xử lý hình ảnh với độ chính xác cao, 
                  bao gồm loại bỏ background, tăng độ phân giải và tạo background mới.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5 text-yellow-600" />
                  NextJS 15
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Framework React hiện đại với App Router, Server Components 
                  và Turbopack để đảm bảo hiệu suất tối ưu.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5 text-green-600" />
                  Supabase
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Database và storage an toàn, đáng tin cậy để lưu trữ 
                  và quản lý hình ảnh của bạn.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Heart className="h-5 w-5 text-red-600" />
                  Shadcn/UI
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Giao diện người dùng đẹp mắt, hiện đại và dễ sử dụng 
                  được xây dựng với Tailwind CSS.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Features */}
        <div className="space-y-6">
          <h2 className="text-2xl font-bold text-center">Tại Sao Chọn Chúng Tôi?</h2>
          
          <div className="grid gap-6 md:grid-cols-3">
            <div className="text-center space-y-3">
              <div className="mx-auto w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                <Zap className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold">Nhanh Chóng</h3>
              <p className="text-sm text-muted-foreground">
                Xử lý hình ảnh chỉ trong vài giây, không cần chờ đợi lâu
              </p>
            </div>

            <div className="text-center space-y-3">
              <div className="mx-auto w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
                <Sparkles className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-semibold">Chất Lượng Cao</h3>
              <p className="text-sm text-muted-foreground">
                Kết quả sắc nét, chuyên nghiệp như được làm bởi designer
              </p>
            </div>

            <div className="text-center space-y-3">
              <div className="mx-auto w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
                <Shield className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold">An Toàn</h3>
              <p className="text-sm text-muted-foreground">
                Hình ảnh được bảo mật và tự động xóa sau 24 giờ
              </p>
            </div>
          </div>
        </div>

        {/* Team */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-6 w-6 text-blue-600" />
              Đội Ngũ Phát Triển
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-center text-muted-foreground">
              Background Generator được phát triển bởi một đội ngũ kỹ sư và designer 
              đam mê công nghệ AI và trải nghiệm người dùng. Chúng tôi luôn lắng nghe 
              phản hồi và không ngừng cải thiện sản phẩm.
            </p>
          </CardContent>
        </Card>

        {/* CTA */}
        <div className="text-center space-y-6 py-8">
          <h2 className="text-2xl font-bold">
            Sẵn Sàng Trải Nghiệm?
          </h2>
          <p className="text-muted-foreground">
            Hãy thử ngay và tạo ra những hình ảnh sản phẩm tuyệt vời
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg">
              <Link href="/upload">
                Bắt Đầu Ngay
                <ArrowRight className="ml-2 h-4 w-4" />
              </Link>
            </Button>
            <Button variant="outline" size="lg" asChild>
              <Link href="/gallery">
                Xem Gallery
              </Link>
            </Button>
          </div>
        </div>

        {/* Contact */}
        <Card>
          <CardHeader>
            <CardTitle>Liên Hệ Với Chúng Tôi</CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">
              Có câu hỏi hoặc cần hỗ trợ? Đừng ngần ngại liên hệ với chúng tôi qua{" "}
              <Link href="/contact" className="text-blue-600 hover:underline">
                trang liên hệ
              </Link>{" "}
              hoặc email: <EMAIL>
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
