import { NextRequest, NextResponse } from "next/server";
import { createClient } from '@supabase/supabase-js';

export async function GET() {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;
    
    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    // Test 1: Check connection
    console.log("Testing Supabase connection...");
    
    // Test 2: List tables
    const { data: tables, error: tablesError } = await supabase
      .from('processed_images')
      .select('count', { count: 'exact', head: true });

    if (tablesError) {
      console.error("Tables error:", tablesError);
      return NextResponse.json({
        success: false,
        error: `Tables error: ${tablesError.message}`,
        details: tablesError
      });
    }

    // Test 3: Try to select from processed_images
    const { data: images, error: selectError } = await supabase
      .from('processed_images')
      .select('*')
      .limit(1);

    if (selectError) {
      console.error("Select error:", selectError);
      return NextResponse.json({
        success: false,
        error: `Select error: ${selectError.message}`,
        details: selectError
      });
    }

    return NextResponse.json({
      success: true,
      message: "Database connection successful",
      data: {
        tablesCount: tables,
        sampleImages: images,
        supabaseUrl: supabaseUrl,
        hasAnonKey: !!supabaseAnonKey
      }
    });

  } catch (error) {
    console.error("Test DB error:", error);
    return NextResponse.json({
      success: false,
      error: error instanceof Error ? error.message : "Unknown error",
      stack: error instanceof Error ? error.stack : undefined
    });
  }
}
