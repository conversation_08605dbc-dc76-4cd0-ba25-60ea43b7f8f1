# 📖 Hướng Dẫn Sử Dụng Chi Tiết - Background Generator Platform

## 🎯 Tổng Quan Nền Tảng

Background Generator là một nền tảng AI tự động xử lý hình ảnh, cho phép người dùng:
- Upload hình ảnh sản phẩm
- Tự động xóa background
- Upscale chất lượng hình ảnh
- Tạo background chuyên nghiệp mới
- Tải xuống kết quả cuối cùng

---

## 🚀 Hướng Dẫn Sử Dụng Từng Bước

### Bước 1: Truy Cập và Đăng Ký/Đăng Nhập

#### 1.1 Truy cập trang chủ
- Mở trình duyệt và truy cập: `http://localhost:3000`
- Giao diện hiển thị trang chủ với các tính năng chính

#### 1.2 Đăng ký tài khoản mới
```
Đường dẫn: /auth/signup
Phương thức: Form submission hoặc Google OAuth
```

**<PERSON><PERSON><PERSON> bước thực hiện:**
1. <PERSON><PERSON> nút "Đăng Ký Miễn <PERSON>" trên trang chủ
2. Điền thông tin:
   - Email: `<EMAIL>`
   - Password: `password123` (tối thiểu 6 ký tự)
   - Confirm Password: `password123`
3. Click "Đăng Ký" hoặc "Đăng Ký với Google"

**Logic xử lý:**
```typescript
// Frontend: src/app/auth/signup/page.tsx
const handleSignup = async (e: React.FormEvent) => {
  // 1. Validate form data
  // 2. Call Supabase Auth API
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
  });

  // 3. Handle response
  if (error) throw error;

  // 4. Redirect to upload page
  router.push('/upload');
};
```

#### 1.3 Đăng nhập
```
Đường dẫn: /auth/login
Phương thức: Email/Password hoặc Google OAuth
```

**Logic xử lý:**
```typescript
// Frontend: src/app/auth/login/page.tsx
const handleLogin = async (e: React.FormEvent) => {
  // 1. Validate credentials
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });

  // 2. Set authentication state
  // 3. Redirect to intended page
};
```

### Bước 2: Upload Hình Ảnh

#### 2.1 Truy cập trang Upload
```
Đường dẫn: /upload
Yêu cầu: Phải đăng nhập (Protected route)
```

**Middleware Protection:**
```typescript
// src/middleware.ts
if (request.nextUrl.pathname === '/upload' && !user) {
  // Redirect to login with return URL
  const redirectUrl = new URL('/auth/login', request.url)
  redirectUrl.searchParams.set('redirectTo', '/upload')
  return NextResponse.redirect(redirectUrl)
}
```

#### 2.2 Chọn phương thức upload

**Phương thức 1: Drag & Drop**
1. Kéo thả file vào vùng "Drag & Drop Zone"
2. Hỗ trợ: JPG, PNG, WEBP (max 10MB)
3. Preview hiển thị ngay lập tức

**Phương thức 2: URL Input**
1. Paste URL hình ảnh vào ô input
2. Click "Tải từ URL"
3. Hệ thống tự động download và validate

**Logic xử lý Upload:**
```typescript
// Frontend: src/app/upload/page.tsx
const handleFileSelect = useCallback((file: File) => {
  // 1. Validate file type and size
  if (!file.type.startsWith('image/')) {
    throw new Error('Invalid file type');
  }

  // 2. Create preview URL
  const preview = URL.createObjectURL(file);

  // 3. Set selected file state
  setSelectedFile({
    id: Date.now().toString(),
    file,
    preview,
    name: file.name,
    size: file.size,
    type: file.type,
  });
}, []);
```

### Bước 3: Xử Lý Hình Ảnh (Processing Pipeline)

#### 3.1 Khởi tạo quá trình xử lý
Click nút "Bắt Đầu Xử Lý" để khởi động pipeline 5 bước:

```typescript
// Processing Steps Definition
const initialSteps: ProcessingStep[] = [
  { id: "upload", name: "Upload", status: "pending", progress: 0 },
  { id: "removeBackground", name: "Xóa Background", status: "pending", progress: 0 },
  { id: "upscale", name: "Tăng Chất Lượng", status: "pending", progress: 0 },
  { id: "generateBackground", name: "Tạo Background", status: "pending", progress: 0 },
  { id: "finalize", name: "Hoàn Thiện", status: "pending", progress: 0 },
];
```

#### 3.2 Step 1: Upload to Server

**Frontend Logic:**
```typescript
// Step 1: Upload
updateStep("upload", { status: "processing", message: "Đang tải lên..." });

const formData = new FormData();
if (selectedFile.file) {
  formData.append("file", selectedFile.file);
} else if (selectedFile.url) {
  formData.append("url", selectedFile.url);
}

const uploadResponse = await fetch("/api/upload", {
  method: "POST",
  body: formData,
});
```

**Backend Logic:**
```typescript
// API: src/app/api/upload/route.ts
export async function POST(request: NextRequest) {
  // 1. Authentication check
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (!user) return 401;

  // 2. Parse form data
  const formData = await request.formData();
  const file = formData.get("file") as File;
  const url = formData.get("url") as string;

  // 3. Generate unique filename
  const uniqueFileName = `original/${timestamp}-${randomId}.${extension}`;

  // 4. Upload to Supabase Storage
  const { data: uploadData, error } = await supabase.storage
    .from("images")
    .upload(uniqueFileName, uploadFile);

  // 5. Save record to database
  const { data: imageRecord } = await supabase
    .from('processed_images')
    .insert({
      original_url: publicUrl,
      status: "uploaded",
    });

  // 6. Return response
  return { imageId: imageRecord.id, publicUrl };
}
```

#### 3.3 Step 2: Remove Background

**API Call:**
```typescript
const removeResponse = await fetch("/api/process/remove-background", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({ imageId: uploadResult.imageId }),
});
```

**Backend Processing:**
```typescript
// API: src/app/api/process/remove-background/route.ts
export async function POST(request: NextRequest) {
  // 1. Get image record from database
  const imageRecord = await getProcessedImage(imageId);

  // 2. Update status to processing
  await updateProcessedImage(imageId, {
    status: "processing_background_removal",
  });

  // 3. Call Runware API
  const processedUrl = await removeBackground(imageRecord.original_url);

  // 4. Update database with result
  await updateProcessedImage(imageId, {
    background_removed_url: processedUrl,
    status: "background_removed",
  });

  return { success: true, processedUrl };
}
```

#### 3.4 Step 3: Upscale Image

**Logic tương tự với Remove Background:**
```typescript
// API: src/app/api/process/upscale/route.ts
// 1. Get background removed image
// 2. Call Runware upscale API
// 3. Update database with upscaled_url
// 4. Return result
```

#### 3.5 Step 4: Generate New Background

**AI Background Generation:**
```typescript
// API: src/app/api/process/generate-background/route.ts
export async function POST(request: NextRequest) {
  // 1. Get upscaled image (or background removed if no upscale)
  const sourceUrl = imageRecord.upscaled_url || imageRecord.background_removed_url;

  // 2. Generate background with AI
  const finalUrl = await generateBackground(sourceUrl, prompt);

  // 3. Update final result
  await updateProcessedImage(imageId, {
    final_url: finalUrl,
    status: "completed",
  });
}
```

#### 3.6 Step 5: Finalize

**Hoàn thiện và chuẩn bị kết quả:**
```typescript
// Frontend: Cập nhật UI state
updateStep("finalize", { status: "completed", progress: 100 });
setState("completed");

// Hiển thị kết quả cuối cùng
setProcessedImage({
  id: result.imageId,
  originalUrl: selectedFile.preview,
  finalUrl: result.finalUrl,
  status: "completed",
});
```

### Bước 4: Xem và Tải Kết Quả

#### 4.1 Giao diện kết quả
- **Before/After Comparison**: So sánh ảnh gốc và ảnh đã xử lý
- **Download Options**: Tải xuống các phiên bản khác nhau
- **Share Options**: Chia sẻ kết quả

#### 4.2 Download Logic
```typescript
// Frontend: src/components/upload/result-display.tsx
const handleDownload = async (url: string, filename: string) => {
  // 1. Fetch image data
  const response = await fetch(url);
  const blob = await response.blob();

  // 2. Create download link
  const downloadUrl = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = downloadUrl;
  link.download = filename;

  // 3. Trigger download
  link.click();
  URL.revokeObjectURL(downloadUrl);
};
```

---

## 🔧 Kiến Trúc Hệ Thống và Data Flow

### Authentication Flow
```
1. User → Login Page → Supabase Auth
2. Supabase → JWT Token → Client Storage
3. Middleware → Validate Token → Protect Routes
4. API Routes → Check Auth → Process Request
```

### Image Processing Pipeline
```
Upload → Supabase Storage → Database Record →
Runware API (Remove BG) → Runware API (Upscale) →
Runware API (Generate BG) → Final Result → Download
```

### Database Schema
```sql
-- processed_images table
CREATE TABLE processed_images (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  original_url TEXT NOT NULL,
  background_removed_url TEXT,
  upscaled_url TEXT,
  final_url TEXT,
  status TEXT DEFAULT 'uploaded',
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
```

### State Management
```typescript
// Upload Page State
type UploadState = "idle" | "uploading" | "processing" | "completed" | "error";

// Processing Steps State
interface ProcessingStep {
  id: string;
  name: string;
  status: 'pending' | 'processing' | 'completed' | 'error';
  progress: number;
  message?: string;
}
```

---

## 🧪 Testing và Debugging

### Test Cases
1. **Authentication Test**: Đăng ký/đăng nhập thành công
2. **Upload Test**: Upload file và URL thành công
3. **Processing Test**: Pipeline 5 bước hoàn thành
4. **Error Handling**: Xử lý lỗi network, file invalid
5. **Download Test**: Tải xuống kết quả thành công

### Debug Points
1. **Network Tab**: Kiểm tra API calls
2. **Console Logs**: Theo dõi state changes
3. **Database**: Verify records được tạo
4. **Storage**: Kiểm tra files được upload

### Common Issues
1. **401 Unauthorized**: Kiểm tra authentication
2. **File Upload Failed**: Kiểm tra file size/type
3. **Processing Stuck**: Kiểm tra Runware API status
4. **Download Failed**: Kiểm tra CORS settings

---

## 🛠️ Advanced Features và Customization

### Custom Background Prompts
```typescript
// Tùy chỉnh prompt cho AI background generation
const customPrompts = {
  professional: "professional product photography background, clean, modern, high quality",
  ecommerce: "white clean background for ecommerce product photography",
  lifestyle: "natural lifestyle background with soft lighting",
  studio: "professional studio background with gradient lighting"
};

// Sử dụng trong API call
const generateResponse = await fetch("/api/process/generate-background", {
  method: "POST",
  headers: { "Content-Type": "application/json" },
  body: JSON.stringify({
    imageId: uploadResult.imageId,
    prompt: customPrompts.professional
  }),
});
```

### Batch Processing
```typescript
// Xử lý nhiều ảnh cùng lúc
const processBatch = async (files: File[]) => {
  const results = await Promise.allSettled(
    files.map(file => processImage(file))
  );

  return results.map((result, index) => ({
    file: files[index],
    status: result.status,
    data: result.status === 'fulfilled' ? result.value : null,
    error: result.status === 'rejected' ? result.reason : null
  }));
};
```

### Real-time Progress Tracking
```typescript
// WebSocket connection cho real-time updates
const useRealtimeProgress = (imageId: string) => {
  const [progress, setProgress] = useState<ProcessingStep[]>([]);

  useEffect(() => {
    const channel = supabase
      .channel(`image-${imageId}`)
      .on('postgres_changes', {
        event: 'UPDATE',
        schema: 'public',
        table: 'processed_images',
        filter: `id=eq.${imageId}`
      }, (payload) => {
        // Update progress based on database changes
        updateProgressFromPayload(payload.new);
      })
      .subscribe();

    return () => supabase.removeChannel(channel);
  }, [imageId]);

  return progress;
};
```

---

## 🚨 Error Handling và Recovery

### Frontend Error Boundaries
```typescript
// Error Boundary Component
class ImageProcessingErrorBoundary extends React.Component {
  constructor(props) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error) {
    return { hasError: true, error };
  }

  componentDidCatch(error, errorInfo) {
    // Log error to monitoring service
    console.error('Image processing error:', error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return (
        <div className="error-fallback">
          <h2>Đã xảy ra lỗi trong quá trình xử lý</h2>
          <button onClick={() => this.setState({ hasError: false })}>
            Thử lại
          </button>
        </div>
      );
    }

    return this.props.children;
  }
}
```

### API Error Handling
```typescript
// Centralized error handling
const handleApiError = (error: any, context: string) => {
  const errorMap = {
    401: "Phiên đăng nhập đã hết hạn. Vui lòng đăng nhập lại.",
    403: "Bạn không có quyền thực hiện thao tác này.",
    413: "File quá lớn. Vui lòng chọn file nhỏ hơn 10MB.",
    429: "Quá nhiều yêu cầu. Vui lòng thử lại sau.",
    500: "Lỗi server. Vui lòng thử lại sau."
  };

  const message = errorMap[error.status] || error.message || "Đã xảy ra lỗi không xác định";

  toast({
    title: `Lỗi ${context}`,
    description: message,
    variant: "destructive",
  });

  // Log to monitoring service
  logError(error, context);
};
```

### Retry Mechanism
```typescript
// Automatic retry with exponential backoff
const retryWithBackoff = async (
  fn: () => Promise<any>,
  maxRetries: number = 3,
  baseDelay: number = 1000
) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn();
    } catch (error) {
      if (attempt === maxRetries) throw error;

      const delay = baseDelay * Math.pow(2, attempt - 1);
      await new Promise(resolve => setTimeout(resolve, delay));
    }
  }
};

// Usage in processing pipeline
const processWithRetry = async (imageId: string) => {
  return retryWithBackoff(async () => {
    const response = await fetch(`/api/process/remove-background`, {
      method: 'POST',
      body: JSON.stringify({ imageId })
    });

    if (!response.ok) throw new Error(`HTTP ${response.status}`);
    return response.json();
  });
};
```

---

## 📊 Performance Monitoring và Analytics

### Performance Metrics
```typescript
// Track processing times
const trackProcessingTime = (step: string, startTime: number) => {
  const endTime = performance.now();
  const duration = endTime - startTime;

  // Send to analytics
  analytics.track('processing_step_completed', {
    step,
    duration,
    timestamp: new Date().toISOString()
  });
};

// Usage
const startTime = performance.now();
await removeBackground(imageId);
trackProcessingTime('remove_background', startTime);
```

### User Analytics
```typescript
// Track user behavior
const trackUserAction = (action: string, properties?: object) => {
  analytics.track(action, {
    userId: user?.id,
    timestamp: new Date().toISOString(),
    ...properties
  });
};

// Examples
trackUserAction('image_uploaded', { fileSize: file.size, fileType: file.type });
trackUserAction('processing_started', { imageId });
trackUserAction('download_completed', { imageId, downloadType: 'final' });
```

---

## 🔧 Development và Deployment

### Environment Setup
```bash
# Development
npm run dev          # Start development server
npm run build        # Build for production
npm run lint         # Run ESLint
npm run type-check   # TypeScript type checking

# Testing
npm run test         # Run unit tests
npm run test:e2e     # Run end-to-end tests
npm run test:coverage # Generate coverage report
```

### Environment Variables
```env
# .env.local
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
RUNWARE_API_KEY=your_runware_api_key
NEXT_PUBLIC_APP_URL=http://localhost:3000
```

### Deployment Checklist
- [ ] Environment variables configured
- [ ] Database migrations applied
- [ ] Storage buckets created
- [ ] API keys validated
- [ ] CORS settings configured
- [ ] SSL certificates installed
- [ ] Monitoring setup
- [ ] Error tracking enabled

---

## 📚 API Reference

### Authentication Endpoints
```typescript
// POST /auth/signup
interface SignupRequest {
  email: string;
  password: string;
}

// POST /auth/login
interface LoginRequest {
  email: string;
  password: string;
}

// POST /auth/logout
// No body required
```

### Image Processing Endpoints
```typescript
// POST /api/upload
interface UploadRequest {
  file?: File;
  url?: string;
}

interface UploadResponse {
  imageId: string;
  url: string;
  publicUrl: string;
}

// POST /api/process/remove-background
interface RemoveBackgroundRequest {
  imageId: string;
}

// POST /api/process/upscale
interface UpscaleRequest {
  imageId: string;
  scale?: number; // Default: 2
}

// POST /api/process/generate-background
interface GenerateBackgroundRequest {
  imageId: string;
  prompt?: string;
}

// GET /api/process/result/{imageId}
interface ProcessResult {
  id: string;
  originalUrl: string;
  backgroundRemovedUrl?: string;
  upscaledUrl?: string;
  finalUrl?: string;
  status: string;
  createdAt: string;
}
```

---

## 🎯 Best Practices

### Code Organization
```
src/
├── app/                 # Next.js App Router
│   ├── api/            # API routes
│   ├── auth/           # Authentication pages
│   └── (pages)/        # Application pages
├── components/         # Reusable components
│   ├── ui/            # UI components (Shadcn)
│   ├── layout/        # Layout components
│   └── features/      # Feature-specific components
├── lib/               # Utility libraries
├── hooks/             # Custom React hooks
├── types/             # TypeScript definitions
└── contexts/          # React contexts
```

### Security Best Practices
1. **Input Validation**: Validate all user inputs
2. **File Type Checking**: Restrict to image files only
3. **Size Limits**: Enforce file size limits
4. **Rate Limiting**: Implement API rate limiting
5. **Authentication**: Protect all sensitive routes
6. **CORS**: Configure proper CORS settings

### Performance Optimization
1. **Image Optimization**: Use Next.js Image component
2. **Lazy Loading**: Implement lazy loading for images
3. **Caching**: Cache API responses where appropriate
4. **Code Splitting**: Split code by routes
5. **Bundle Analysis**: Monitor bundle size

---

*Tài liệu này cung cấp hướng dẫn toàn diện để sử dụng, phát triển và maintain nền tảng Background Generator.*
