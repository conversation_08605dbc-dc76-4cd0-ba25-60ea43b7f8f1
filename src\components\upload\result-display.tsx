"use client";

import { useState } from "react";
import Image from "next/image";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { 
  Download, 
  Share2, 
  Eye, 
  RotateCcw,
  Heart,
  ExternalLink
} from "lucide-react";
import { ProcessedImage } from "@/types";
import { useToast } from "@/hooks/use-toast";

interface ResultDisplayProps {
  result: ProcessedImage;
  onDownload: (url: string, filename: string) => void;
  onSaveToGallery?: (imageId: string) => void;
  onStartOver?: () => void;
}

export function ResultDisplay({ 
  result, 
  onDownload, 
  onSaveToGallery,
  onStartOver 
}: ResultDisplayProps) {
  const [activeTab, setActiveTab] = useState("final");
  const { toast } = useToast();

  const handleShare = async () => {
    if (navigator.share && result.finalUrl) {
      try {
        await navigator.share({
          title: "Background Generator Result",
          text: "Xem kết quả tạo background từ Background Generator",
          url: result.finalUrl,
        });
      } catch (error) {
        // Fallback to clipboard
        await navigator.clipboard.writeText(result.finalUrl);
        toast({
          title: "Đã sao chép",
          description: "Link đã được sao chép vào clipboard",
        });
      }
    } else if (result.finalUrl) {
      await navigator.clipboard.writeText(result.finalUrl);
      toast({
        title: "Đã sao chép",
        description: "Link đã được sao chép vào clipboard",
      });
    }
  };

  const handleViewFullSize = (url: string) => {
    window.open(url, "_blank");
  };

  const images = [
    {
      id: "original",
      label: "Ảnh gốc",
      url: result.originalUrl,
      description: "Hình ảnh ban đầu"
    },
    {
      id: "background-removed",
      label: "Đã xóa background",
      url: result.backgroundRemovedUrl,
      description: "Background đã được loại bỏ"
    },
    {
      id: "upscaled",
      label: "Đã tăng độ phân giải",
      url: result.upscaledUrl,
      description: "Chất lượng đã được cải thiện"
    },
    {
      id: "final",
      label: "Kết quả cuối",
      url: result.finalUrl,
      description: "Sản phẩm với background mới"
    }
  ].filter(img => img.url);

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Eye className="h-5 w-5" />
            Kết Quả Xử Lý
          </CardTitle>
          <Badge variant="default" className="bg-green-100 text-green-800">
            Hoàn thành
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-6">
        {/* Image Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-2 lg:grid-cols-4">
            {images.map((image) => (
              <TabsTrigger key={image.id} value={image.id} className="text-xs">
                {image.label}
              </TabsTrigger>
            ))}
          </TabsList>
          
          {images.map((image) => (
            <TabsContent key={image.id} value={image.id} className="space-y-4">
              <div className="relative aspect-square w-full max-w-md mx-auto overflow-hidden rounded-lg border">
                <Image
                  src={image.url!}
                  alt={image.description}
                  fill
                  className="object-contain"
                />
                <Button
                  variant="secondary"
                  size="sm"
                  className="absolute top-2 right-2"
                  onClick={() => handleViewFullSize(image.url!)}
                >
                  <ExternalLink className="h-4 w-4" />
                </Button>
              </div>
              <div className="text-center">
                <h3 className="font-medium">{image.label}</h3>
                <p className="text-sm text-muted-foreground">{image.description}</p>
              </div>
            </TabsContent>
          ))}
        </Tabs>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-3 justify-center">
          {result.finalUrl && (
            <Button
              onClick={() => onDownload(result.finalUrl!, `background-generated-${result.id}.png`)}
              className="flex-1 sm:flex-none"
            >
              <Download className="mr-2 h-4 w-4" />
              Tải xuống kết quả
            </Button>
          )}
          
          {result.backgroundRemovedUrl && (
            <Button
              variant="outline"
              onClick={() => onDownload(result.backgroundRemovedUrl!, `background-removed-${result.id}.png`)}
              className="flex-1 sm:flex-none"
            >
              <Download className="mr-2 h-4 w-4" />
              Tải background đã xóa
            </Button>
          )}
          
          <Button
            variant="outline"
            onClick={handleShare}
            className="flex-1 sm:flex-none"
          >
            <Share2 className="mr-2 h-4 w-4" />
            Chia sẻ
          </Button>
          
          {onSaveToGallery && (
            <Button
              variant="outline"
              onClick={() => onSaveToGallery(result.id)}
              className="flex-1 sm:flex-none"
            >
              <Heart className="mr-2 h-4 w-4" />
              Lưu vào Gallery
            </Button>
          )}
        </div>

        {/* Start Over */}
        {onStartOver && (
          <div className="text-center pt-4 border-t">
            <Button
              variant="ghost"
              onClick={onStartOver}
              className="text-muted-foreground hover:text-foreground"
            >
              <RotateCcw className="mr-2 h-4 w-4" />
              Xử lý ảnh khác
            </Button>
          </div>
        )}

        {/* Metadata */}
        <div className="text-xs text-muted-foreground space-y-1 pt-4 border-t">
          <p>ID: {result.id}</p>
          <p>Tạo lúc: {new Date(result.createdAt).toLocaleString('vi-VN')}</p>
          <p>Trạng thái: {result.status}</p>
        </div>
      </CardContent>
    </Card>
  );
}
