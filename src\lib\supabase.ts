import { createClient } from '@supabase/supabase-js';
import { createBrowserClient } from '@supabase/ssr';
import { Database } from '@/types';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

// Client for browser/frontend with SSR support
export const supabase = createBrowserClient<Database>(supabaseUrl, supabaseAnonKey);

// Legacy client for compatibility
export const supabaseClient = createClient<Database>(supabaseUrl, supabaseAnonKey);

// Storage bucket name
export const STORAGE_BUCKET = 'images';

// Helper functions for file upload
export const uploadImage = async (file: File, path: string) => {
  const { data, error } = await supabase.storage
    .from(STORAGE_BUCKET)
    .upload(path, file, {
      cacheControl: '3600',
      upsert: false
    });

  if (error) {
    throw new Error(`Upload failed: ${error.message}`);
  }

  return data;
};

export const getPublicUrl = (path: string) => {
  const { data } = supabase.storage
    .from(STORAGE_BUCKET)
    .getPublicUrl(path);

  return data.publicUrl;
};

export const deleteImage = async (path: string) => {
  const { error } = await supabase.storage
    .from(STORAGE_BUCKET)
    .remove([path]);

  if (error) {
    throw new Error(`Delete failed: ${error.message}`);
  }
};

// Database operations
export const saveProcessedImage = async (imageData: Database['public']['Tables']['processed_images']['Insert']) => {
  const { data, error } = await supabase
    .from('processed_images')
    .insert(imageData)
    .select()
    .single();

  if (error) {
    throw new Error(`Database insert failed: ${error.message}`);
  }

  return data;
};

export const updateProcessedImage = async (
  id: string,
  updates: Database['public']['Tables']['processed_images']['Update']
) => {
  const { data, error } = await supabase
    .from('processed_images')
    .update(updates)
    .eq('id', id)
    .select()
    .single();

  if (error) {
    throw new Error(`Database update failed: ${error.message}`);
  }

  return data;
};

export const getProcessedImage = async (id: string) => {
  const { data, error } = await supabase
    .from('processed_images')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    throw new Error(`Database select failed: ${error.message}`);
  }

  return data;
};

export const getAllProcessedImages = async (limit = 50, offset = 0) => {
  const { data, error } = await supabase
    .from('processed_images')
    .select('*')
    .order('created_at', { ascending: false })
    .range(offset, offset + limit - 1);

  if (error) {
    throw new Error(`Database select failed: ${error.message}`);
  }

  return data;
};
