// Test authentication with Supabase
const { createClient } = require('@supabase/supabase-js');

const supabaseUrl = 'https://svbfdivnopohjqjwddcv.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InN2YmZkaXZub3BvaGpxandkZGN2Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDgwOTM4NzksImV4cCI6MjA2MzY2OTg3OX0.a7w9ymXIQsnWOsqSA16DfiOwu8oW9HxYVMtst6kmgck';

const supabase = createClient(supabaseUrl, supabaseKey);

async function testAuth() {
  console.log('Testing Supabase Authentication...');

  try {
    // Test sign up first
    console.log('\n1. Testing sign up...');
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: '<EMAIL>',
      password: 'password123'
    });

    if (signUpError && signUpError.message !== 'User already registered') {
      console.error('Sign up error:', signUpError.message);
      return;
    }

    console.log('✅ Sign up successful or user exists!');

    // Test sign in
    console.log('\n2. Testing sign in...');
    const { data, error } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'password123'
    });

    if (error) {
      console.error('Sign in error:', error.message);
      return;
    }

    console.log('✅ Sign in successful!');
    console.log('User:', data.user.email);
    console.log('Session:', data.session ? 'Active' : 'None');

    // Test getting current user
    console.log('\n3. Testing get user...');
    const { data: userData, error: userError } = await supabase.auth.getUser();

    if (userError) {
      console.error('Get user error:', userError.message);
    } else {
      console.log('✅ Current user:', userData.user?.email || 'None');
    }

    // Test sign out
    console.log('\n4. Testing sign out...');
    const { error: signOutError } = await supabase.auth.signOut();

    if (signOutError) {
      console.error('Sign out error:', signOutError.message);
    } else {
      console.log('✅ Sign out successful!');
    }

  } catch (error) {
    console.error('Unexpected error:', error);
  }
}

testAuth();
