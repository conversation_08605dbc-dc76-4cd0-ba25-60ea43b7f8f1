import { NextRequest, NextResponse } from "next/server";
import { getProcessedImage } from "@/lib/supabase";
import { ApiResponse, ProcessedImage } from "@/types";

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    if (!id) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: "Image ID is required",
      }, { status: 400 });
    }

    // Get image record from database
    const imageRecord = await getProcessedImage(id);
    
    if (!imageRecord) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: "Image not found",
      }, { status: 404 });
    }

    // Transform database record to ProcessedImage type
    const processedImage: ProcessedImage = {
      id: imageRecord.id,
      originalUrl: imageRecord.original_url,
      processedUrl: imageRecord.processed_url || "",
      backgroundRemovedUrl: imageRecord.background_removed_url || undefined,
      upscaledUrl: imageRecord.upscaled_url || undefined,
      finalUrl: imageRecord.final_url || undefined,
      createdAt: imageRecord.created_at,
      status: imageRecord.status as "processing" | "completed" | "error",
      steps: [], // Steps are not stored in database, would need separate table for this
    };

    return NextResponse.json<ApiResponse<ProcessedImage>>({
      success: true,
      data: processedImage,
      message: "Image retrieved successfully",
    });

  } catch (error) {
    console.error("Get result error:", error);
    
    return NextResponse.json<ApiResponse>({
      success: false,
      error: error instanceof Error ? error.message : "Failed to get result",
    }, { status: 500 });
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { id } = params;

    if (!id) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: "Image ID is required",
      }, { status: 400 });
    }

    // Get image record first
    const imageRecord = await getProcessedImage(id);
    
    if (!imageRecord) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: "Image not found",
      }, { status: 404 });
    }

    // Delete from storage (optional - implement if needed)
    // const { deleteImage } = await import("@/lib/supabase");
    // if (imageRecord.original_url) {
    //   await deleteImage(getPathFromUrl(imageRecord.original_url));
    // }

    // Delete from database
    const { supabase } = await import("@/lib/supabase");
    const { error } = await supabase
      .from("processed_images")
      .delete()
      .eq("id", id);

    if (error) {
      throw new Error(`Database delete failed: ${error.message}`);
    }

    return NextResponse.json<ApiResponse>({
      success: true,
      message: "Image deleted successfully",
    });

  } catch (error) {
    console.error("Delete result error:", error);
    
    return NextResponse.json<ApiResponse>({
      success: false,
      error: error instanceof Error ? error.message : "Failed to delete image",
    }, { status: 500 });
  }
}
