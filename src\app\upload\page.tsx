"use client";

import { useState, useCallback } from "react";
import { DragDropZone } from "@/components/upload/drag-drop-zone";
import { UrlInput } from "@/components/upload/url-input";
import { StepByStepProcessor } from "@/components/upload/step-by-step-processor";
import { ResultDisplay } from "@/components/upload/result-display";
import { useToast } from "@/hooks/use-toast";
import { ProcessedImage, ImageUpload } from "@/types";

type UploadState = "idle" | "selecting" | "processing" | "completed";

export default function UploadPage() {
  const [state, setState] = useState<UploadState>("idle");
  const [selectedFile, setSelectedFile] = useState<ImageUpload | null>(null);
  const [result, setResult] = useState<ProcessedImage | null>(null);
  const { toast } = useToast();



  const handleFileSelect = useCallback((file: File) => {
    const preview = URL.createObjectURL(file);
    setSelectedFile({
      id: Date.now().toString(),
      file,
      preview,
      name: file.name,
      size: file.size,
      type: file.type,
    });
    setState("idle");
  }, []);

  const handleUrlSubmit = useCallback(async (url: string) => {
    try {
      setState("selecting");

      setSelectedFile({
        id: Date.now().toString(),
        url,
        preview: url,
        name: "Image from URL",
        size: 0,
        type: "image/*",
      });

      setState("idle");
    } catch (error) {
      toast({
        title: "Lỗi",
        description: "Không thể tải hình ảnh từ URL",
        variant: "destructive",
      });
      setState("idle");
    }
  }, [toast]);

  const handleFileRemove = useCallback(() => {
    if (selectedFile?.preview && selectedFile.file) {
      URL.revokeObjectURL(selectedFile.preview);
    }
    setSelectedFile(null);
    setState("idle");
  }, [selectedFile]);

  const handleProcessingComplete = (result: ProcessedImage) => {
    setResult(result);
    setState("completed");
  };

  const handleDownload = async (url: string, filename: string) => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      const downloadUrl = URL.createObjectURL(blob);

      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      URL.revokeObjectURL(downloadUrl);

      toast({
        title: "Tải xuống thành công",
        description: `Đã tải xuống ${filename}`,
      });
    } catch (error) {
      toast({
        title: "Lỗi tải xuống",
        description: "Không thể tải xuống file",
        variant: "destructive",
      });
    }
  };

  const handleStartOver = () => {
    setSelectedFile(null);
    setResult(null);
    setState("idle");
  };

  return (
    <div className="container py-8">
      <div className="mx-auto max-w-4xl space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold tracking-tight">
            Upload & Xử Lý Hình Ảnh
          </h1>
          <p className="text-lg text-muted-foreground">
            Tải lên hình ảnh sản phẩm và để AI tạo background chuyên nghiệp
          </p>
        </div>

        {/* Upload Section */}
        {state === "idle" && (
          <div className="grid gap-8 md:grid-cols-2">
            <DragDropZone
              onFileSelect={handleFileSelect}
              onFileRemove={handleFileRemove}
              selectedFile={selectedFile?.file}
              preview={selectedFile?.preview}
            />
            <UrlInput onUrlSubmit={handleUrlSubmit} />
          </div>
        )}

        {/* Step by Step Processing */}
        {selectedFile && state === "idle" && (
          <StepByStepProcessor
            selectedFile={selectedFile.file}
            selectedUrl={selectedFile.url}
            onComplete={handleProcessingComplete}
            onReset={handleStartOver}
          />
        )}

        {/* Results */}
        {state === "completed" && result && (
          <ResultDisplay
            result={result}
            onDownload={handleDownload}
            onStartOver={handleStartOver}
          />
        )}
      </div>
    </div>
  );
}
