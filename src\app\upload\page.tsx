"use client";

import { useState, useCallback } from "react";
import { DragDropZone } from "@/components/upload/drag-drop-zone";
import { UrlInput } from "@/components/upload/url-input";
import { ProcessingProgress } from "@/components/upload/processing-progress";
import { ResultDisplay } from "@/components/upload/result-display";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { useToast } from "@/hooks/use-toast";
import { ProcessingStep, ProcessedImage, ImageUpload } from "@/types";
import { Upload, Sparkles } from "lucide-react";

type UploadState = "idle" | "uploading" | "processing" | "completed" | "error";

export default function UploadPage() {
  const [state, setState] = useState<UploadState>("idle");
  const [selectedFile, setSelectedFile] = useState<ImageUpload | null>(null);
  const [processingSteps, setProcessingSteps] = useState<ProcessingStep[]>([]);
  const [result, setResult] = useState<ProcessedImage | null>(null);
  const { toast } = useToast();

  const createProcessingSteps = (): ProcessingStep[] => [
    {
      id: "upload",
      name: "Tải lên hình ảnh",
      status: "pending",
      progress: 0,
    },
    {
      id: "removeBackground",
      name: "Xóa background",
      status: "pending",
      progress: 0,
    },
    {
      id: "upscale",
      name: "Tăng độ phân giải",
      status: "pending",
      progress: 0,
    },
    {
      id: "generateBackground",
      name: "Tạo background mới",
      status: "pending",
      progress: 0,
    },
    {
      id: "finalize",
      name: "Hoàn thiện",
      status: "pending",
      progress: 0,
    },
  ];

  const updateStep = (stepId: string, updates: Partial<ProcessingStep>) => {
    setProcessingSteps(prev => 
      prev.map(step => 
        step.id === stepId ? { ...step, ...updates } : step
      )
    );
  };

  const handleFileSelect = useCallback((file: File) => {
    const preview = URL.createObjectURL(file);
    setSelectedFile({
      id: Date.now().toString(),
      file,
      preview,
      name: file.name,
      size: file.size,
      type: file.type,
    });
    setState("idle");
  }, []);

  const handleUrlSubmit = useCallback(async (url: string) => {
    try {
      setState("uploading");
      
      // Tạo một file object từ URL
      const response = await fetch(url);
      const blob = await response.blob();
      const file = new File([blob], "image-from-url", { type: blob.type });
      
      setSelectedFile({
        id: Date.now().toString(),
        url,
        preview: url,
        name: "Image from URL",
        size: blob.size,
        type: blob.type,
      });
      
      setState("idle");
    } catch (error) {
      toast({
        title: "Lỗi",
        description: "Không thể tải hình ảnh từ URL",
        variant: "destructive",
      });
      setState("idle");
    }
  }, [toast]);

  const handleFileRemove = useCallback(() => {
    if (selectedFile?.preview && selectedFile.file) {
      URL.revokeObjectURL(selectedFile.preview);
    }
    setSelectedFile(null);
    setState("idle");
  }, [selectedFile]);

  const handleStartProcessing = async () => {
    if (!selectedFile) return;

    setState("processing");
    const steps = createProcessingSteps();
    setProcessingSteps(steps);

    try {
      // Step 1: Upload
      updateStep("upload", { status: "processing", message: "Đang tải lên..." });
      
      const formData = new FormData();
      if (selectedFile.file) {
        formData.append("file", selectedFile.file);
      } else if (selectedFile.url) {
        formData.append("url", selectedFile.url);
      }

      const uploadResponse = await fetch("/api/upload", {
        method: "POST",
        body: formData,
      });

      if (!uploadResponse.ok) {
        throw new Error("Upload failed");
      }

      const uploadResult = await uploadResponse.json();
      updateStep("upload", { status: "completed", progress: 100 });

      // Step 2: Remove Background
      updateStep("removeBackground", { 
        status: "processing", 
        message: "Đang xóa background..." 
      });

      const removeBackgroundResponse = await fetch("/api/process/remove-background", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ imageId: uploadResult.imageId }),
      });

      if (!removeBackgroundResponse.ok) {
        throw new Error("Background removal failed");
      }

      updateStep("removeBackground", { status: "completed", progress: 100 });

      // Step 3: Upscale
      updateStep("upscale", { 
        status: "processing", 
        message: "Đang tăng độ phân giải..." 
      });

      const upscaleResponse = await fetch("/api/process/upscale", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ imageId: uploadResult.imageId }),
      });

      if (!upscaleResponse.ok) {
        throw new Error("Upscaling failed");
      }

      updateStep("upscale", { status: "completed", progress: 100 });

      // Step 4: Generate Background
      updateStep("generateBackground", { 
        status: "processing", 
        message: "Đang tạo background mới..." 
      });

      const generateResponse = await fetch("/api/process/generate-background", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ imageId: uploadResult.imageId }),
      });

      if (!generateResponse.ok) {
        throw new Error("Background generation failed");
      }

      updateStep("generateBackground", { status: "completed", progress: 100 });

      // Step 5: Finalize
      updateStep("finalize", { 
        status: "processing", 
        message: "Đang hoàn thiện..." 
      });

      const finalizeResponse = await fetch(`/api/process/result/${uploadResult.imageId}`);
      
      if (!finalizeResponse.ok) {
        throw new Error("Failed to get result");
      }

      const finalResult = await finalizeResponse.json();
      updateStep("finalize", { status: "completed", progress: 100 });

      setResult(finalResult.data);
      setState("completed");

      toast({
        title: "Thành công!",
        description: "Hình ảnh đã được xử lý thành công",
      });

    } catch (error) {
      console.error("Processing error:", error);
      
      // Mark current processing step as error
      const currentStep = processingSteps.find(step => step.status === "processing");
      if (currentStep) {
        updateStep(currentStep.id, { 
          status: "error", 
          message: error instanceof Error ? error.message : "Đã xảy ra lỗi" 
        });
      }

      setState("error");
      toast({
        title: "Lỗi xử lý",
        description: "Đã xảy ra lỗi trong quá trình xử lý hình ảnh",
        variant: "destructive",
      });
    }
  };

  const handleDownload = async (url: string, filename: string) => {
    try {
      const response = await fetch(url);
      const blob = await response.blob();
      const downloadUrl = URL.createObjectURL(blob);
      
      const link = document.createElement("a");
      link.href = downloadUrl;
      link.download = filename;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      
      URL.revokeObjectURL(downloadUrl);
      
      toast({
        title: "Tải xuống thành công",
        description: `Đã tải xuống ${filename}`,
      });
    } catch (error) {
      toast({
        title: "Lỗi tải xuống",
        description: "Không thể tải xuống file",
        variant: "destructive",
      });
    }
  };

  const handleStartOver = () => {
    setSelectedFile(null);
    setProcessingSteps([]);
    setResult(null);
    setState("idle");
  };

  const handleCancel = () => {
    // TODO: Implement actual cancellation logic
    handleStartOver();
    toast({
      title: "Đã hủy",
      description: "Quá trình xử lý đã được hủy",
    });
  };

  return (
    <div className="container py-8">
      <div className="mx-auto max-w-4xl space-y-8">
        {/* Header */}
        <div className="text-center space-y-4">
          <h1 className="text-3xl font-bold tracking-tight">
            Upload & Xử Lý Hình Ảnh
          </h1>
          <p className="text-lg text-muted-foreground">
            Tải lên hình ảnh sản phẩm và để AI tạo background chuyên nghiệp
          </p>
        </div>

        {/* Upload Section */}
        {state === "idle" && (
          <div className="grid gap-8 md:grid-cols-2">
            <DragDropZone
              onFileSelect={handleFileSelect}
              onFileRemove={handleFileRemove}
              selectedFile={selectedFile?.file}
              preview={selectedFile?.preview}
            />
            <UrlInput onUrlSubmit={handleUrlSubmit} />
          </div>
        )}

        {/* Selected File Preview */}
        {selectedFile && state === "idle" && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                Hình Ảnh Đã Chọn
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center justify-between">
                <div>
                  <p className="font-medium">{selectedFile.name}</p>
                  <p className="text-sm text-muted-foreground">
                    {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
                  </p>
                </div>
                <button
                  onClick={handleStartProcessing}
                  className="inline-flex items-center justify-center rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 bg-primary text-primary-foreground hover:bg-primary/90 h-10 px-4 py-2"
                >
                  <Sparkles className="mr-2 h-4 w-4" />
                  Bắt Đầu Xử Lý
                </button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Processing */}
        {(state === "processing" || state === "error") && (
          <ProcessingProgress
            steps={processingSteps}
            onCancel={handleCancel}
            canCancel={state === "processing"}
          />
        )}

        {/* Results */}
        {state === "completed" && result && (
          <ResultDisplay
            result={result}
            onDownload={handleDownload}
            onStartOver={handleStartOver}
          />
        )}
      </div>
    </div>
  );
}
