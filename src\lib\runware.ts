import { RunwareTask, RunwareResponse } from '@/types';

const RUNWARE_API_KEY = process.env.RUNWARE_API_KEY!;
const RUNWARE_API_URL = process.env.RUNWARE_API_URL || 'https://api.runware.ai';

class RunwareClient {
  private apiKey: string;
  private baseUrl: string;

  constructor(apiKey: string, baseUrl: string) {
    this.apiKey = apiKey;
    this.baseUrl = baseUrl;
  }

  private async makeRequest(tasks: RunwareTask[]): Promise<RunwareResponse[]> {
    const response = await fetch(`${this.baseUrl}/v1`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.apiKey}`,
      },
      body: JSON.stringify(tasks),
    });

    if (!response.ok) {
      throw new Error(`Runware API error: ${response.status} ${response.statusText}`);
    }

    const data = await response.json();
    return data;
  }

  async removeBackground(imageUrl: string): Promise<string> {
    // Generate UUID v4 for task
    const taskUUID = crypto.randomUUID();

    const task: RunwareTask = {
      taskType: 'imageBackgroundRemoval',
      taskUUID: taskUUID,
      inputImage: imageUrl,
      outputFormat: 'PNG',
      outputType: 'URL',
      model: 'runware:110@1', // Bria RMBG 2.0 - good balance of quality and speed
    };

    const results = await this.makeRequest([task]);
    const result = results[0];

    if (result.error) {
      throw new Error(`Background removal failed: ${result.error}`);
    }

    if (!result.imageURL) {
      throw new Error('No image URL returned from background removal');
    }

    return result.imageURL;
  }

  async upscaleImage(imageUrl: string, scale: number = 2): Promise<string> {
    // Generate UUID v4 for task
    const taskUUID = crypto.randomUUID();

    // Validate scale factor (must be 2, 3, or 4)
    if (![2, 3, 4].includes(scale)) {
      throw new Error('Upscale factor must be 2, 3, or 4');
    }

    const task: RunwareTask = {
      taskType: 'imageUpscale',
      taskUUID: taskUUID,
      inputImage: imageUrl,
      outputFormat: 'PNG',
      outputType: 'URL',
      upscaleFactor: scale,
    };

    const results = await this.makeRequest([task]);
    const result = results[0];

    if (result.error) {
      throw new Error(`Image upscaling failed: ${result.error}`);
    }

    if (!result.imageURL) {
      throw new Error('No image URL returned from upscaling');
    }

    return result.imageURL;
  }

  async generateBackground(
    productImageUrl: string,
    prompt: string = "professional product photography background, clean, modern, high quality"
  ): Promise<string> {
    // Generate UUID v4 for task
    const taskUUID = crypto.randomUUID();

    const task: RunwareTask = {
      taskType: 'imageInference',
      taskUUID: taskUUID,
      seedImage: productImageUrl, // Use seedImage for image-to-image generation
      outputFormat: 'PNG',
      outputType: 'URL',
      positivePrompt: prompt,
      negativePrompt: "blurry, low quality, distorted, ugly, bad anatomy, extra limbs",
      model: "runware:100@1", // SDXL base model
      steps: 25,
      CFGScale: 7,
      seed: Math.floor(Math.random() * 1000000),
      width: 1024,
      height: 1024,
      strength: 0.7, // Control how much the original image influences the result
      scheduler: "DPM++ 2M Karras",
    };

    const results = await this.makeRequest([task]);
    const result = results[0];

    if (result.error) {
      throw new Error(`Background generation failed: ${result.error}`);
    }

    if (!result.imageURL) {
      throw new Error('No image URL returned from background generation');
    }

    return result.imageURL;
  }

  async enhanceImage(
    imageUrl: string,
    prompt: string = "high quality, sharp details, professional photography, 8k resolution"
  ): Promise<string> {
    // Generate UUID v4 for task
    const taskUUID = crypto.randomUUID();

    const task: RunwareTask = {
      taskType: 'imageInference',
      taskUUID: taskUUID,
      seedImage: imageUrl, // Use seedImage for image-to-image enhancement
      outputFormat: 'PNG',
      outputType: 'URL',
      positivePrompt: prompt,
      negativePrompt: "blurry, low quality, noise, artifacts, distorted",
      model: "runware:100@1", // SDXL base model
      steps: 30,
      CFGScale: 7.5,
      seed: Math.floor(Math.random() * 1000000),
      width: 1024,
      height: 1024,
      strength: 0.3, // Lower strength to preserve original image
      scheduler: "DPM++ 2M Karras",
    };

    const results = await this.makeRequest([task]);
    const result = results[0];

    if (result.error) {
      throw new Error(`Image enhancement failed: ${result.error}`);
    }

    if (!result.imageURL) {
      throw new Error('No image URL returned from enhancement');
    }

    return result.imageURL;
  }
}

// Export singleton instance
export const runwareClient = new RunwareClient(RUNWARE_API_KEY, RUNWARE_API_URL);

// Export individual functions for easier use
export const removeBackground = (imageUrl: string) => runwareClient.removeBackground(imageUrl);
export const upscaleImage = (imageUrl: string, scale?: number) => runwareClient.upscaleImage(imageUrl, scale);
export const generateBackground = (productImageUrl: string, prompt?: string) =>
  runwareClient.generateBackground(productImageUrl, prompt);
export const enhanceImage = (imageUrl: string, prompt?: string) =>
  runwareClient.enhanceImage(imageUrl, prompt);
