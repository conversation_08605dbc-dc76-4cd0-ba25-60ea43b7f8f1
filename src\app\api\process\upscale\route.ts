import { NextRequest, NextResponse } from "next/server";
import { getProcessedImage, updateProcessedImage } from "@/lib/supabase";
import { upscaleImage } from "@/lib/runware";
import { ApiResponse } from "@/types";

export async function POST(request: NextRequest) {
  try {
    const { imageId, scale = 2 } = await request.json();

    if (!imageId) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: "Image ID is required",
      }, { status: 400 });
    }

    // Get image record from database
    const imageRecord = await getProcessedImage(imageId);
    
    if (!imageRecord) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: "Image not found",
      }, { status: 404 });
    }

    if (!imageRecord.background_removed_url) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: "Background must be removed first",
      }, { status: 400 });
    }

    // Update status to processing
    await updateProcessedImage(imageId, {
      status: "processing_upscale",
    });

    try {
      // Upscale image using Runware API
      const upscaledUrl = await upscaleImage(imageRecord.background_removed_url, scale);

      // Update database with result
      const updatedRecord = await updateProcessedImage(imageId, {
        upscaled_url: upscaledUrl,
        status: "upscaled",
      });

      return NextResponse.json<ApiResponse>({
        success: true,
        data: {
          imageId: updatedRecord.id,
          upscaledUrl: upscaledUrl,
          status: updatedRecord.status,
        },
        message: "Image upscaled successfully",
      });

    } catch (runwareError) {
      // Update status to error
      await updateProcessedImage(imageId, {
        status: "error",
      });

      throw runwareError;
    }

  } catch (error) {
    console.error("Upscale error:", error);
    
    return NextResponse.json<ApiResponse>({
      success: false,
      error: error instanceof Error ? error.message : "Upscaling failed",
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json<ApiResponse>({
    success: false,
    error: "Method not allowed",
  }, { status: 405 });
}
