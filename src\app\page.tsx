"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON>R<PERSON>, <PERSON>ap, Spark<PERSON>, <PERSON>, Clock } from "lucide-react";
import { useAuth } from "@/contexts/auth-context";

export default function Home() {
  const { user } = useAuth();
  return (
    <div className="flex flex-col">
      {/* Hero Section */}
      <section className="relative overflow-hidden bg-gradient-to-br from-blue-50 via-white to-purple-50 py-20 sm:py-32">
        <div className="container relative">
          <div className="mx-auto max-w-4xl text-center">
            <Badge variant="secondary" className="mb-4">
              <Sparkles className="mr-1 h-3 w-3" />
              Powered by AI
            </Badge>
            <h1 className="text-4xl font-bold tracking-tight text-gray-900 sm:text-6xl">
              Tạo <PERSON>yê<PERSON>{" "}
              <span className="bg-gradient-to-r from-blue-600 to-purple-600 bg-clip-text text-transparent">
                Cho Sản Phẩm
              </span>
            </h1>
            <p className="mt-6 text-lg leading-8 text-gray-600">
              Sử dụng AI để tự động loại bỏ background và tạo background mới chuyên nghiệp
              cho hình ảnh sản phẩm của bạn. Nhanh chóng, chất lượng cao, dễ sử dụng.
            </p>
            <div className="mt-10 flex items-center justify-center gap-x-6">
              {user ? (
                <Button asChild size="lg" className="h-12 px-8">
                  <Link href="/upload">
                    Bắt Đầu Ngay
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              ) : (
                <Button asChild size="lg" className="h-12 px-8">
                  <Link href="/auth/signup">
                    Đăng Ký Miễn Phí
                    <ArrowRight className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
              )}
              <Button variant="outline" size="lg" asChild className="h-12 px-8">
                <Link href="/gallery">
                  Xem Demo
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 sm:py-32">
        <div className="container">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Tính Năng Nổi Bật
            </h2>
            <p className="mt-4 text-lg text-gray-600">
              Công nghệ AI tiên tiến giúp bạn tạo ra những hình ảnh sản phẩm chuyên nghiệp
            </p>
          </div>

          <div className="mx-auto mt-16 grid max-w-5xl grid-cols-1 gap-8 sm:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader>
                <Zap className="h-8 w-8 text-blue-600" />
                <CardTitle>Xử Lý Nhanh</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Xử lý hình ảnh chỉ trong vài giây với công nghệ AI tiên tiến
                </CardDescription>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Sparkles className="h-8 w-8 text-purple-600" />
                <CardTitle>Chất Lượng Cao</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Kết quả sắc nét, chuyên nghiệp như được chụp bởi photographer
                </CardDescription>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Shield className="h-8 w-8 text-green-600" />
                <CardTitle>An Toàn</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Hình ảnh của bạn được bảo mật và tự động xóa sau 24h
                </CardDescription>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <Clock className="h-8 w-8 text-orange-600" />
                <CardTitle>Tiết Kiệm Thời Gian</CardTitle>
              </CardHeader>
              <CardContent>
                <CardDescription>
                  Không cần Photoshop hay kỹ năng chỉnh sửa phức tạp
                </CardDescription>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gray-50 py-20 sm:py-32">
        <div className="container">
          <div className="mx-auto max-w-2xl text-center">
            <h2 className="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl">
              Sẵn Sàng Bắt Đầu?
            </h2>
            <p className="mt-4 text-lg text-gray-600">
              Tạo background chuyên nghiệp cho sản phẩm của bạn ngay hôm nay
            </p>
            <div className="mt-8">
              <Button asChild size="lg" className="h-12 px-8">
                <Link href="/upload">
                  Upload Hình Ảnh Ngay
                  <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
}
