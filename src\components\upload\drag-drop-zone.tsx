"use client";

import { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { Upload, X, Image as ImageIcon } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import Image from "next/image";

interface DragDropZoneProps {
  onFileSelect: (file: File) => void;
  onFileRemove: () => void;
  selectedFile?: File;
  preview?: string;
  className?: string;
  disabled?: boolean;
}

export function DragDropZone({
  onFileSelect,
  onFileRemove,
  selectedFile,
  preview,
  className,
  disabled = false,
}: DragDropZoneProps) {
  const [dragActive, setDragActive] = useState(false);

  const onDrop = useCallback(
    (acceptedFiles: File[]) => {
      if (acceptedFiles.length > 0 && !disabled) {
        onFileSelect(acceptedFiles[0]);
      }
      setDragActive(false);
    },
    [onFileSelect, disabled]
  );

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: {
      "image/*": [".jpeg", ".jpg", ".png", ".webp"],
    },
    multiple: false,
    disabled,
    onDragEnter: () => setDragActive(true),
    onDragLeave: () => setDragActive(false),
  });

  if (selectedFile && preview) {
    return (
      <Card className={cn("relative overflow-hidden", className)}>
        <CardContent className="p-0">
          <div className="relative aspect-square w-full">
            <Image
              src={preview}
              alt="Preview"
              fill
              className="object-cover"
            />
            <div className="absolute inset-0 bg-black/40 opacity-0 transition-opacity hover:opacity-100">
              <div className="flex h-full items-center justify-center">
                <Button
                  variant="destructive"
                  size="sm"
                  onClick={onFileRemove}
                  disabled={disabled}
                >
                  <X className="h-4 w-4 mr-2" />
                  Xóa
                </Button>
              </div>
            </div>
          </div>
          <div className="p-4">
            <p className="text-sm font-medium truncate">{selectedFile.name}</p>
            <p className="text-xs text-muted-foreground">
              {(selectedFile.size / 1024 / 1024).toFixed(2)} MB
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card
      {...getRootProps()}
      className={cn(
        "cursor-pointer border-2 border-dashed transition-colors",
        isDragActive || dragActive
          ? "border-primary bg-primary/5"
          : "border-muted-foreground/25 hover:border-primary/50",
        disabled && "cursor-not-allowed opacity-50",
        className
      )}
    >
      <CardContent className="flex flex-col items-center justify-center p-8 text-center">
        <input {...getInputProps()} />
        
        <div className="mb-4 rounded-full bg-muted p-4">
          {isDragActive || dragActive ? (
            <Upload className="h-8 w-8 text-primary" />
          ) : (
            <ImageIcon className="h-8 w-8 text-muted-foreground" />
          )}
        </div>

        <div className="space-y-2">
          <h3 className="text-lg font-semibold">
            {isDragActive || dragActive
              ? "Thả file vào đây"
              : "Kéo thả hoặc click để chọn ảnh"}
          </h3>
          <p className="text-sm text-muted-foreground">
            Hỗ trợ: JPG, PNG, WEBP (tối đa 10MB)
          </p>
        </div>

        <Button
          variant="outline"
          className="mt-4"
          disabled={disabled}
          type="button"
        >
          Chọn File
        </Button>
      </CardContent>
    </Card>
  );
}
