import { NextRequest, NextResponse } from "next/server";
import { getProcessedImage, updateProcessedImage } from "@/lib/supabase";
import { removeBackground } from "@/lib/runware";
import { ApiResponse } from "@/types";

export async function POST(request: NextRequest) {
  try {
    const { imageId } = await request.json();

    if (!imageId) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: "Image ID is required",
      }, { status: 400 });
    }

    // Get image record from database
    const imageRecord = await getProcessedImage(imageId);
    
    if (!imageRecord) {
      return NextResponse.json<ApiResponse>({
        success: false,
        error: "Image not found",
      }, { status: 404 });
    }

    // Update status to processing
    await updateProcessedImage(imageId, {
      status: "processing_background_removal",
    });

    try {
      // Remove background using Runware API
      const backgroundRemovedUrl = await removeBackground(imageRecord.original_url);

      // Update database with result
      const updatedRecord = await updateProcessedImage(imageId, {
        background_removed_url: backgroundRemovedUrl,
        status: "background_removed",
      });

      return NextResponse.json<ApiResponse>({
        success: true,
        data: {
          imageId: updatedRecord.id,
          backgroundRemovedUrl: backgroundRemovedUrl,
          status: updatedRecord.status,
        },
        message: "Background removed successfully",
      });

    } catch (runwareError) {
      // Update status to error
      await updateProcessedImage(imageId, {
        status: "error",
      });

      throw runwareError;
    }

  } catch (error) {
    console.error("Remove background error:", error);
    
    return NextResponse.json<ApiResponse>({
      success: false,
      error: error instanceof Error ? error.message : "Background removal failed",
    }, { status: 500 });
  }
}

export async function GET() {
  return NextResponse.json<ApiResponse>({
    success: false,
    error: "Method not allowed",
  }, { status: 405 });
}
