"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Link, Loader2 } from "lucide-react";
import { useToast } from "@/hooks/use-toast";

interface UrlInputProps {
  onUrlSubmit: (url: string) => void;
  disabled?: boolean;
}

export function UrlInput({ onUrlSubmit, disabled = false }: UrlInputProps) {
  const [url, setUrl] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const validateUrl = (url: string): boolean => {
    try {
      const urlObj = new URL(url);
      return urlObj.protocol === "http:" || urlObj.protocol === "https:";
    } catch {
      return false;
    }
  };

  const isImageUrl = (url: string): boolean => {
    const imageExtensions = [".jpg", ".jpeg", ".png", ".webp", ".gif"];
    const urlLower = url.toLowerCase();
    return imageExtensions.some(ext => urlLower.includes(ext));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!url.trim()) {
      toast({
        title: "Lỗi",
        description: "Vui lòng nhập URL hình ảnh",
        variant: "destructive",
      });
      return;
    }

    if (!validateUrl(url)) {
      toast({
        title: "URL không hợp lệ",
        description: "Vui lòng nhập URL hợp lệ (bắt đầu với http:// hoặc https://)",
        variant: "destructive",
      });
      return;
    }

    if (!isImageUrl(url)) {
      toast({
        title: "Cảnh báo",
        description: "URL có vẻ không phải là hình ảnh. Bạn có chắc chắn muốn tiếp tục?",
        variant: "destructive",
      });
    }

    setIsLoading(true);
    
    try {
      // Kiểm tra xem URL có thể truy cập được không
      const response = await fetch(url, { method: "HEAD" });
      
      if (!response.ok) {
        throw new Error("Không thể truy cập URL");
      }

      const contentType = response.headers.get("content-type");
      if (contentType && !contentType.startsWith("image/")) {
        toast({
          title: "Không phải hình ảnh",
          description: "URL này không trỏ đến một file hình ảnh",
          variant: "destructive",
        });
        return;
      }

      onUrlSubmit(url);
      setUrl("");
      
      toast({
        title: "Thành công",
        description: "Đã tải hình ảnh từ URL",
      });
    } catch (error) {
      toast({
        title: "Lỗi",
        description: "Không thể tải hình ảnh từ URL này. Vui lòng kiểm tra lại.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Link className="h-5 w-5" />
          Hoặc nhập URL hình ảnh
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="image-url">URL hình ảnh</Label>
            <Input
              id="image-url"
              type="url"
              placeholder="https://example.com/image.jpg"
              value={url}
              onChange={(e) => setUrl(e.target.value)}
              disabled={disabled || isLoading}
            />
          </div>
          
          <Button
            type="submit"
            disabled={disabled || isLoading || !url.trim()}
            className="w-full"
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Đang tải...
              </>
            ) : (
              "Tải từ URL"
            )}
          </Button>
        </form>
        
        <div className="mt-4 text-xs text-muted-foreground">
          <p>• URL phải bắt đầu với http:// hoặc https://</p>
          <p>• Hỗ trợ các định dạng: JPG, PNG, WEBP, GIF</p>
          <p>• Hình ảnh phải có thể truy cập công khai</p>
        </div>
      </CardContent>
    </Card>
  );
}
