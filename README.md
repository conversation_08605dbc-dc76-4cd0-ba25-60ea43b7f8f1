# Background Generator - T<PERSON>o Background <PERSON><PERSON><PERSON><PERSON> <PERSON>hiệ<PERSON> <PERSON>ản Phẩm

Website tự động tạo background chuyên nghiệp cho hình ảnh sản phẩm sử dụng AI với NextJS 15 và Runware API.

## ✨ Tính Năng

- 🚀 **Xử lý nhanh**: Tạo background chỉ trong vài giây
- 🎨 **Chất lượng cao**: <PERSON><PERSON><PERSON> qu<PERSON> sắc nét, chuyên nghiệp
- 🔒 **An toàn**: Hình ảnh tự động xóa sau 24h
- 📱 **Responsive**: Hoạt động tốt trên mọi thiết bị
- 🎯 **Dễ sử dụng**: <PERSON><PERSON><PERSON> di<PERSON>n trực quan, không cần kỹ năng chỉnh sửa

## 🛠️ Công Nghệ Sử Dụng

- **Frontend**: NextJS 15, React 19, TypeScript
- **Styling**: Tailwind CSS, Shadcn/UI
- **Backend**: NextJS API Routes
- **Database & Storage**: Supabase
- **Image Processing**: Runware API
- **Deployment**: Vercel (recommended)

## 🚀 Bắt Đầu

### Yêu C<PERSON>u Hệ Thống

- Node.js 18+
- npm hoặc yarn
- Tài khoản Supabase
- Tài khoản Runware API

### Cài Đặt

1. **Clone repository**
```bash
git clone <repository-url>
cd background-generator
```

2. **Cài đặt dependencies**
```bash
npm install
```

3. **Cấu hình environment variables**
```bash
cp .env.example .env.local
```

Cập nhật `.env.local` với thông tin của bạn:
```env
# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Runware API Configuration
RUNWARE_API_KEY=your_runware_api_key
RUNWARE_API_URL=https://api.runware.ai

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3001
```

4. **Chạy development server**
```bash
npm run dev
```

Mở [http://localhost:3001](http://localhost:3001) để xem ứng dụng.

## 📁 Cấu Trúc Dự Án

```
background-generator/
├── src/
│   ├── app/                    # App Router (NextJS 15)
│   │   ├── api/               # API routes
│   │   ├── upload/            # Upload page
│   │   ├── gallery/           # Gallery page
│   │   ├── docs/              # Documentation
│   │   ├── about/             # About page
│   │   ├── contact/           # Contact page
│   │   └── page.tsx           # Homepage
│   ├── components/            # Reusable components
│   │   ├── ui/               # Shadcn/UI components
│   │   ├── layout/           # Layout components
│   │   ├── upload/           # Upload related components
│   │   └── gallery/          # Gallery components
│   ├── lib/                  # Utility libraries
│   │   ├── supabase.ts      # Supabase client
│   │   ├── runware.ts       # Runware API client
│   │   └── utils.ts         # General utilities
│   ├── hooks/               # Custom React hooks
│   └── types/               # TypeScript type definitions
├── public/                  # Static assets
└── config files            # Various config files
```

## 🔧 Cấu Hình Supabase

### 1. Tạo Database Schema

```sql
-- Create processed_images table
CREATE TABLE processed_images (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    original_url TEXT NOT NULL,
    processed_url TEXT,
    background_removed_url TEXT,
    upscaled_url TEXT,
    final_url TEXT,
    status TEXT DEFAULT 'uploaded',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

### 2. Tạo Storage Bucket

```sql
-- Create storage bucket for images
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES (
    'images',
    'images',
    true,
    10485760, -- 10MB limit
    ARRAY['image/jpeg', 'image/jpg', 'image/png', 'image/webp']
);
```

## 📖 API Documentation

### Upload Image
```bash
POST /api/upload
Content-Type: multipart/form-data

# Body: file hoặc url
```

### Process Image
```bash
# Remove background
POST /api/process/remove-background
Content-Type: application/json
{"imageId": "uuid"}

# Upscale image
POST /api/process/upscale
Content-Type: application/json
{"imageId": "uuid", "scale": 2}

# Generate background
POST /api/process/generate-background
Content-Type: application/json
{"imageId": "uuid", "prompt": "professional background"}
```

### Get Result
```bash
GET /api/process/result/{id}
```

## 🚀 Deployment

### Vercel (Recommended)

1. Push code lên GitHub
2. Kết nối repository với Vercel
3. Cấu hình environment variables
4. Deploy

### Environment Variables cho Production

Đảm bảo cấu hình đầy đủ các biến môi trường:
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY`
- `RUNWARE_API_KEY`
- `NEXT_PUBLIC_APP_URL`

## 🤝 Đóng Góp

1. Fork repository
2. Tạo feature branch (`git checkout -b feature/AmazingFeature`)
3. Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. Push to branch (`git push origin feature/AmazingFeature`)
5. Mở Pull Request

## 📝 License

Distributed under the MIT License. See `LICENSE` for more information.

## 📞 Liên Hệ

- Website: [Background Generator](http://localhost:3001)
- Email: <EMAIL>

## 🙏 Acknowledgments

- [NextJS](https://nextjs.org/)
- [Supabase](https://supabase.com/)
- [Runware](https://runware.ai/)
- [Shadcn/UI](https://ui.shadcn.com/)
- [Tailwind CSS](https://tailwindcss.com/)
